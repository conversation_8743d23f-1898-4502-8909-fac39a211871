import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';

class LoListDrawer extends StatefulWidget {
  final VoidCallback onClose;
  final List<Map<String, dynamic>>? loList;

  const LoListDrawer({
    super.key,
    required this.onClose,
    this.loList,
  });

  @override
  State<LoListDrawer> createState() => _LoListDrawerState();
}

class _LoListDrawerState extends State<LoListDrawer> {
  final ScrollController _scrollController = ScrollController();
  
  // Expansion states for hierarchy
  Map<String, bool> _loExpandedStates = {};
  Map<String, bool> _entityExpandedStates = {};

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use actual API data from widget.loList
    final List<Map<String, dynamic>> loList = widget.loList ?? [];

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Drawer(
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        child: Column(
          children: [
            // const SizedBox(height: 40),
            _buildHeader(),
            Expanded(
              child: _buildContent(loList),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md, vertical: AppSpacing.size10),
      decoration: BoxDecoration(
        color: Color(0XFFCBDDFF),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'LO List',
              textAlign: TextAlign.left,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: widget.onClose,
            child: Container(
              width: 30,
              height: 30,
              decoration: const BoxDecoration(
                color: Color(0xffE5EBFD),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 16,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(List<Map<String, dynamic>> loList) {
    if (loList.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No LO data available',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: loList.length,
      itemBuilder: (context, index) {
        final lo = loList[index];
        return Column(
          children: [
            _buildFlatLoItem(lo, index),
            if (index != loList.length - 1)
              Divider(
                height: 1,
                thickness: 1,
                color: Colors.grey.shade300,
              ),
          ],
        );
      },
    );
  }

  // Build individual LO item for flat list (similar to GO structure but for LOs)
  Widget _buildFlatLoItem(Map<String, dynamic> lo, int index) {
    final loId = 'flat_${lo['id'] ?? 'lo_$index'}';
    final isLoExpanded = _loExpandedStates[loId] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                // 1. Collapse all other LOs first (flat list behavior)
                _loExpandedStates.forEach((key, value) {
                  if (key != loId) {
                    _loExpandedStates[key] = false;
                    // Clear their entity states to save memory
                    _entityExpandedStates.removeWhere((entityKey, _) => entityKey.startsWith(key));
                  }
                });

                // 2. Toggle current LO
                _loExpandedStates[loId] = !isLoExpanded;

                // 3. If collapsing, reset its entity states (clean slate)
                if (!_loExpandedStates[loId]!) {
                  _entityExpandedStates.removeWhere((key, _) => key.startsWith(loId));
                }
              });
            },
            child: Container(
              margin: EdgeInsets.only(
                left: AppSpacing.md,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
                right: AppSpacing.md,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${index + 1}. ${lo['name'] ?? 'LO Title'}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: isLoExpanded ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isLoExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: isLoExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isLoExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (lo['entities'] != null && (lo['entities'] as List).isNotEmpty)
                    ...(lo['entities'] as List).asMap().entries.map((entityEntry) {
                      final entityIndex = entityEntry.key;
                      final entity = entityEntry.value;
                      return Column(
                        children: [
                          _buildEntityItem(entity, entityIndex, loId),
                          if (entityIndex != (lo['entities'] as List).length - 1)
                            Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No Entities available',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEntityItem(Map<String, dynamic> entity, int entityIndex, String loId) {
    final entityId = '${loId}_${entity['id'] ?? 'entity_$entityIndex'}';
    final isEntityExpanded = _entityExpandedStates[entityId] ?? false;

    return Container(
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                // Collapse all other entities in this LO first
                _entityExpandedStates.forEach((key, value) {
                  if (key.startsWith(loId) && key != entityId) {
                    _entityExpandedStates[key] = false;
                  }
                });
                _entityExpandedStates[entityId] = !isEntityExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: AppSpacing.xs, horizontal: AppSpacing.md),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: AppSpacing.lg),
                      child: Text(
                        '${entityIndex + 1}. ${entity['name'] ?? 'Entity Title'}',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: isEntityExpanded ? FontWeight.w600 : FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  Icon(
                    isEntityExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: isEntityExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isEntityExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (entity['attributes'] != null && (entity['attributes'] as List).isNotEmpty)
                    ...(entity['attributes'] as List).asMap().entries.map((attributeEntry) {
                      final attributeIndex = attributeEntry.key;
                      final attribute = attributeEntry.value;
                      return Column(
                        children: [
                          if (attributeIndex > 0)
                            Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                          _buildAttributeItem(attribute, attributeIndex),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No Attributes available',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAttributeItem(Map<String, dynamic> attribute, int attributeIndex) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: AppSpacing.xs, horizontal: AppSpacing.xl),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: AppSpacing.md),
              child: Text(
                '${attributeIndex + 1}. ${attribute['name'] ?? 'Attribute Name'}',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
