import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/models/agent_data.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/models/solution/solution_session_model.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/web_my_library_screen.dart';
import 'package:nsl/screens/web/new_design/web_solutions_screen.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/nsl_thinking_message_loader.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/solution_artifacts_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_chat_history.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_ellipsis.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/hover_button_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/quick_message_button.dart';
import 'package:nsl/screens/web/static_flow/chat_message_bubble_static.dart';
import 'package:nsl/screens/web/static_flow/new_existing%20_project_model.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_entity_version_dropdown.dart';
import 'package:nsl/screens/web/static_flow/global_library_accordion_flow.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/ocr_text_side_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/workflow_side_panel.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/conversation_response.dart';

import 'dart:convert';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/workflow_data_provider.dart';
import 'package:nsl/screens/web/new_design/workflow_tree_builder.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_role_card.dart';

import 'package:nsl/widgets/custom_checkbox.dart';
import 'package:nsl/widgets/resizable_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/user_profile_card.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/screens/web/new_design/widgets/book_solution_app_bar.dart';
import 'package:nsl/screens/web/static_flow/manual_creation_static_Screen.dart';

class WebHomeScreenChatStatic extends StatefulWidget {
  const WebHomeScreenChatStatic({super.key});

  @override
  State<WebHomeScreenChatStatic> createState() =>
      WebHomeScreenChatStaticState();
}

class WebHomeScreenChatStaticState extends State<WebHomeScreenChatStatic>
    with TickerProviderStateMixin {
  Map<String, dynamic> imageData = {};
  bool isImagesLoading = true;

  // Animation controllers for the three columns
  late AnimationController _firstColumnController;
  late AnimationController _secondColumnController;
  late AnimationController _thirdColumnController;

  // Animation controllers for background images
  late AnimationController _firstBgController;
  late AnimationController _secondBgController;
  late AnimationController _thirdBgController;

  // Animation controllers for main content images
  late AnimationController _firstContentController;
  late AnimationController _secondContentController;
  late AnimationController _thirdContentController;

  // Animations for columns
  late Animation<Offset> _firstColumnAnimation;
  late Animation<Offset> _secondColumnAnimation;
  late Animation<Offset> _thirdColumnAnimation;

  // Animations for background images
  late Animation<double> _firstBgAnimation;
  late Animation<double> _secondBgAnimation;
  late Animation<double> _thirdBgAnimation;

  // Scale animations for background images (stack effect)
  late Animation<double> _firstBgScaleAnimation;
  late Animation<double> _secondBgScaleAnimation;
  late Animation<double> _thirdBgScaleAnimation;

  // Animations for main content images
  late Animation<double> _firstContentAnimation;
  late Animation<double> _secondContentAnimation;
  late Animation<double> _thirdContentAnimation;

  // initState is implemented below

  // Load image data from JSON file
  Future<void> _loadImageData() async {
    try {
      final String response =
          await rootBundle.loadString('assets/data/images_data.json');
      setState(() {
        imageData = json.decode(response);
        isImagesLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading image data: $e');
      setState(() {
        isImagesLoading = false;
      });
    }
  }

  late TextEditingController chatController;
  // messages is now managed by the WebHomeProviderStatic
  // currentScreenIndex is now managed by the WebHomeProviderStatic
  bool isLoading = false;
  bool isLoadingWorkflows = false; // Loading state for workflows
  bool isAudioLoading =
      false; // Loading state for audio recording/transcription
  bool isLoadingChatSession =
      false; // Loading state for chat session operations
  bool showSidePanel = false;
  bool hasTextInChatField = false; // Track if there's text in the chat field
  bool isOcrExist = false;

  // Helper method to access the provider's messages list
  List<ChatMessage> get messages =>
      Provider.of<WebHomeProviderStatic>(context, listen: false).messages;

  // OCR side panel state
  bool showOcrPanel = false;
  String ocrText = '';
  String ocrFileName = '';

  // File upload state for the new UI
  bool isFileUploading =
      false; // Active file upload process (selection, upload, transmission)
  bool isFileUploaded = false; // Completed upload state (file ready for use)
  bool isFileProcessing = false; // File processing state (OCR, analysis)
  String uploadedFileName = '';
  String uploadedFileText = '';
  FileUploadOcrResponse? fileUploadOcrResponse;

  // Multimedia service
  final MultimediaService _multimediaService = MultimediaService();

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Audio player
  final AudioPlayer audioPlayer = AudioPlayer();

  // Audio playback state
  String? currentPlayingMessageId;
  bool isPlaying = false;
  bool isPaused = false;
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  String? currentAudioFilePath;

  // Recording state
  bool isRecording = false;

  // Speech recognition state
  String _recognizedText = "";

  bool isHovered = false;
  bool isSolutionHovered = false;

  // Hover states for background shape transform effects
  bool isFirstColumnHovered = false;
  bool isSecondColumnHovered = false;
  bool isThirdColumnHovered = false;
  bool isManualHovered = false;

  // Add file upload and OCR messages to chat
  void addFileUploadAndOcrMessages(String fileName, String extractedText,
      FileUploadOcrResponse ocrResponseModel) {
    setState(() {
      ocrFileName = fileName;
      ocrText = extractedText;
      showOcrPanel = true;
      chatController.text = extractedText;

      // Update file upload states for the ChatField component
      isFileUploading = false; // Upload process complete
      isFileUploaded = true; // File successfully uploaded and ready for use
      isFileProcessing = false; // OCR processing complete
      uploadedFileName = fileName;
      uploadedFileText = extractedText;
      fileUploadOcrResponse = ocrResponseModel;
    });

    // Store the extracted text in the provider for the next API call
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    provider.lastUserMessageForApi = extractedText;

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Handle file selection from the ChatField component
  void _handleFileSelection(String fileName, String filePath) {
    // Start file upload process
    setState(() {
      isFileUploading = true; // Active upload process
      isFileUploaded = false; // Not yet completed
      isFileProcessing = false; // Not yet processing
    });

    // Use the multimedia service to process the file
    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      // Add the file upload and OCR messages to chat
      addFileUploadAndOcrMessages(fileName, extractedText, ocrResponseModel);

      setState(() {
        isFileUploading = false; // Upload process complete
        isFileProcessing = false; // Processing complete
      });
    };

    // Process the file
    _multimediaService.processFile(context);
  }

  // Role-related variables
  RoleInfo? selectedRole;
  bool isLoadingRoles = false;
  // Global EntitiesData variable
  EntitiesData globalEntitiesData = EntitiesData();
  // Global AgentData variable
  AgentData globalAgentData = AgentData();
  // Global storage for EntityElement data with sections from API
  Map<String, EntityElement> globalEntityElements = {};
  // Entity-related variables
  Entity? selectedEntity;

  // Track selected section index for section abbreviations
  int selectedSectionIndex = 0;
  bool _isAIEnabled = true; // Add AI toggle state
  bool _isChatEnabled = false; // Add chat toggle state

  // Track selected tab for sections with tabs
  Map<String, String> selectedEntityTabs = {};

  bool isLoadingEntities = false;

  // We'll use the provider's session ID instead of storing it locally

  List localData = ["role", "agent", "entity", "workflow", "entities"];

  // Circuit board tab selection
  String selectedCircuitTab = 'Synthetic';

  // Quick message selection
  String? get selectedQuickMessage =>
      Provider.of<WebHomeProviderStatic>(context, listen: false)
          .selectedQuickMessage;
  // = "Solution";

  // Workflow-related variables
  List<Map<String, dynamic>> workflows = [];
  Map<String, dynamic> workflowSystemInfo = {};
  Map<String, dynamic>? selectedWorkflow;
  String? selectedWorkflowVersion;
  List<String> workflowVersions = [];

  // Chat history expansion state is now managed by the WebHomeProviderStatic

  // NSL Thinking expansion state - maps message index to expansion state
  Map<int, bool> nslThinkingExpanded = {};

  // Side panel resizing
  double sidePanelWidth = 250.0; // Default width

  // ScrollController for workflow tree horizontal scrolling
  final ScrollController _workflowTreeScrollController = ScrollController();
  double minSidePanelWidth = 250.0; // Minimum width
  double maxSidePanelWidth = 600.0; // Maximum width
  bool isResizing = false;

  // Track if WebMyLibraryScreen should be shown
  bool showWebMyLibraryScreen = false;
  bool showWebSolutionsScreen = false;
  bool showChatSessionList = false;

  // Track which column item is selected
  String? selectedColumnItem;

  // Track visibility of the three main sections
  bool showEducationIcons = true;
  bool showChatSessions = true;
  bool showAIIcons = true;

  // Handle new project creation
  void _handleNewProjectSelected() {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    provider.showBookSolutionAppBar = true;
    provider.showSolutionDetailsPanel = true;
    provider.isProjectCreated = true;
    _sendMessage();
    Logger.info(
        'New project selected - BookSolutionAppBar and SolutionDetailsPanel will be shown');
  }

  // Handle existing project selection
  void _handleExistingProjectSelected() {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    provider.showBookSolutionAppBar = true;
    provider.showSolutionDetailsPanel = true;
    provider.isProjectCreated = true;
    _sendMessage();
    Logger.info(
        'Existing project selected - BookSolutionAppBar and SolutionDetailsPanel will be shown');
  }

  // Handle Inspirations click
  void _handleInspirationsClick() {
    setState(() {
      if (selectedColumnItem == 'Inspirations') {
        // If already selected, deselect and show sections again
        selectedColumnItem = null;
        showWebMyLibraryScreen = false;
        showEducationIcons = true;
        showChatSessions = true;
        showAIIcons = true;
      } else {
        // Select Inspirations and hide the three main sections
        selectedColumnItem = 'Inspirations';
        showWebMyLibraryScreen = true;
        showWebSolutionsScreen = false;
        showChatSessionList = false;
        showEducationIcons = false;
        showChatSessions = false;
        showAIIcons = false;
      }
    });
    Logger.info(
        'Inspirations clicked - selectedColumnItem: $selectedColumnItem, showWebMyLibraryScreen: $showWebMyLibraryScreen');
  }

  void _handleSolutionsClick() {
    setState(() {
      if (selectedColumnItem == 'Solutions') {
        // If already selected, deselect and show sections again
        selectedColumnItem = null;
        showWebSolutionsScreen = false;
        showEducationIcons = true;
        showChatSessions = true;
        showAIIcons = true;
      } else {
        // Select Solutions and hide the three main sections
        selectedColumnItem = 'Solutions';
        showWebMyLibraryScreen = false;
        showChatSessionList = false;
        showWebSolutionsScreen = true;
        showEducationIcons = false;
        showChatSessions = false;
        showAIIcons = false;
      }
    });
    Logger.info(
        'Solutions clicked - selectedColumnItem: $selectedColumnItem, showWebSolutionsScreen: $showWebSolutionsScreen');
  }

  // Handle My Chats click
  void _handleChatClick() {
    setState(() {
      if (selectedColumnItem == 'My Chats') {
        // If already selected, deselect and show sections again
        selectedColumnItem = null;
        showChatSessionList = false;
        showEducationIcons = true;
        showChatSessions = true;
        showAIIcons = true;
      } else {
        // Select My Chats and hide the three main sections
        selectedColumnItem = 'My Chats';
        showWebMyLibraryScreen = false;
        showWebSolutionsScreen = false;
        showChatSessionList = true;
        showEducationIcons = false;
        showChatSessions = false;
        showAIIcons = false;
      }
    });
    Logger.info(
        'My Chats clicked - selectedColumnItem: $selectedColumnItem, showChatSessionList: $showChatSessionList');
  }

  // Profile tooltip overlay
  OverlayEntry? _profileTooltipOverlay;

  // Flag to track which tooltip is currently active
  String? _activeTooltipType;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _initializeAnimationControllers();

    // Initialize with no selected role or entity
    selectedRole = null;
    selectedEntity = null;
    selectedWorkflow = null;
    // Load image data from JSON
    _loadImageData();
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    chatController = provider.chatController;
    // Add listener to chat controller to detect text changes
    chatController.addListener(() {
      final hasText = chatController.text.trim().isNotEmpty;
      if (hasText != hasTextInChatField) {
        setState(() {
          hasTextInChatField = hasText;
        });
      }
    });

    // Initialize multimedia service
    _multimediaService.initialize();

    // Set up multimedia service callbacks
    _multimediaService.onStateChanged = () {
      if (mounted) {
        setState(() {
          // Update UI state based on multimedia service state
          isPlaying = _multimediaService.isPlaying;
          isPaused = _multimediaService.isPaused;
          isRecording = _multimediaService.isRecording;
          currentPlayingMessageId = _multimediaService.currentPlayingMessageId;
          currentPosition = _multimediaService.currentPosition;
          totalDuration = _multimediaService.totalDuration;
        });
      }
    };

    // Set up additional multimedia service listeners for text recognition
    _setupMultimediaServiceListeners();

    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      if (mounted) {
        // Add file upload and OCR messages to chat
        setState(() {
          ocrFileName = fileName;
          ocrText = extractedText;
          showOcrPanel = true;
          chatController.text = extractedText;
          fileUploadOcrResponse = ocrResponseModel;
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    };

    // Note: Workflow data will be loaded directly from API responses
    // We don't preload workflow data from JSON anymore

    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        // Load chat history
        await loadChatHistory();

        // Fetch available modes
        // await provider.fetchModes();

        // Set up listener for WebHomeProviderStatic's currentScreenIndex changes
        _setupWebHomeProviderListener();
      },
    );
  }

  // Initialize animation controllers
  void _initializeAnimationControllers() {
    // Column animation controllers
    _firstColumnController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    _secondColumnController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    _thirdColumnController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );

    // Background image animation controllers
    _firstBgController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    _secondBgController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    _thirdBgController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );

    // Content image animation controllers
    _firstContentController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    _secondContentController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    _thirdContentController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );

    // Column slide animations
    _firstColumnAnimation = Tween<Offset>(
      begin: Offset(-1.0, 0.0), // Start from left
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _firstColumnController,
      curve: Curves.easeOutCubic,
    ));

    _secondColumnAnimation = Tween<Offset>(
      begin: Offset(0.0, -1.0), // Start from top
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _secondColumnController,
      curve: Curves.easeOutCubic,
    ));

    _thirdColumnAnimation = Tween<Offset>(
      begin: Offset(1.0, 0.0), // Start from right
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _thirdColumnController,
      curve: Curves.easeOutCubic,
    ));

    // Background image fade animations
    _firstBgAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _firstBgController,
      curve: Curves.easeInOut,
    ));

    _secondBgAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _secondBgController,
      curve: Curves.easeInOut,
    ));

    _thirdBgAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _thirdBgController,
      curve: Curves.easeInOut,
    ));

    // Background image scale animations for stack effect
    _firstBgScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _firstBgController,
      curve: Curves.elasticOut,
    ));

    _secondBgScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _secondBgController,
      curve: Curves.elasticOut,
    ));

    _thirdBgScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _thirdBgController,
      curve: Curves.elasticOut,
    ));

    // Content image fade animations
    _firstContentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _firstContentController,
      curve: Curves.easeInOut,
    ));

    _secondContentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _secondContentController,
      curve: Curves.easeInOut,
    ));

    _thirdContentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _thirdContentController,
      curve: Curves.easeInOut,
    ));

    // Start animations with delays
    _startAnimations();
  }

  // Start animations with proper delays for stack effect
  void _startAnimations() {
    // Start first column animation immediately
    _firstColumnController.forward();

    // Start first column background image immediately with stack effect (positioned image comes first)
    _firstBgController.forward();

    // Start first column content image with significant delay (book/education image comes after stack)
    Future.delayed(Duration(milliseconds: 800), () {
      if (mounted) _firstContentController.forward();
    });

    // Start second column animation with delay
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) _secondColumnController.forward();
    });

    // Start second column background image immediately when column starts with stack effect
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) _secondBgController.forward();
    });

    // Start second column content image with significant delay (chat content comes after stack)
    Future.delayed(Duration(milliseconds: 1100), () {
      if (mounted) _secondContentController.forward();
    });

    // Start third column animation with delay
    Future.delayed(Duration(milliseconds: 600), () {
      if (mounted) _thirdColumnController.forward();
    });

    // Start third column background image immediately when column starts with stack effect
    Future.delayed(Duration(milliseconds: 600), () {
      if (mounted) _thirdBgController.forward();
    });

    // Start third column content image with significant delay (AI icons come after stack)
    Future.delayed(Duration(milliseconds: 1400), () {
      if (mounted) _thirdContentController.forward();
    });
  }

  // Set up listener for WebHomeProviderStatic's currentScreenIndex changes
  void _setupWebHomeProviderListener() {
    if (!mounted) return;

    try {
      final webHomeProvider =
          Provider.of<WebHomeProviderStatic>(context, listen: false);

      // Add listener for changes to currentScreenIndex
      webHomeProvider.addListener(() {
        if (mounted) {
          // When currentScreenIndex changes, log the change
          final currentIndex = webHomeProvider.currentScreenIndex;
          Logger.info(
              'WebHomeProviderStatic currentScreenIndex changed to: $currentIndex');
        }
      });
    } catch (e) {
      Logger.error('Error setting up WebHomeProviderStatic listener: $e');
    }
  }

  // Set up multimedia service listeners for text recognition
  void _setupMultimediaServiceListeners() {
    if (!mounted) return;

    try {
      // Set up a listener for recognized text
      _multimediaService.onTextRecognized = (text) {
        if (mounted) {
          setState(() {
            // Update recognized text in the chat field
            _recognizedText = text;
            chatController.text = text;

            // Log the recognized text
            Logger.info('Text recognized from audio: $text');
          });
        }
      };
    } catch (e) {
      Logger.error('Error setting up multimedia service listeners: $e');
    }
  }

  @override
  void dispose() {
    // Clean up the controllers when the widget is disposed
    // chatController.dispose();
    _chatScrollController.dispose();
    _hideAllTooltips(); // Make sure to remove any active tooltips

    // Dispose animation controllers
    _firstColumnController.dispose();
    _secondColumnController.dispose();
    _thirdColumnController.dispose();
    _firstBgController.dispose();
    _secondBgController.dispose();
    _thirdBgController.dispose();
    _firstContentController.dispose();
    _secondContentController.dispose();
    _thirdContentController.dispose();

    // Dispose audio player
    audioPlayer.dispose();

    // Dispose recording service
    _multimediaService.dispose();

    // Remove WebHomeProviderStatic listener
    try {
      final webHomeProvider =
          Provider.of<WebHomeProviderStatic>(context, listen: false);
      webHomeProvider.removeListener(() {});
    } catch (e) {
      Logger.error('Error removing WebHomeProviderStatic listener: $e');
    }

    super.dispose();
  }

  // Hide profile tooltip
  void hideProfileTooltip() {
    _profileTooltipOverlay?.remove();
    _profileTooltipOverlay = null;
    if (_activeTooltipType == 'profile') {
      _activeTooltipType = null;
    }
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Workflow tooltip overlay
  OverlayEntry? _workflowTooltipOverlay;

  // Track the last click time to prevent tooltip from showing during clicks
  DateTime _lastClickTime = DateTime.now();

  // Show workflow profile tooltip positioned near the workflow title
  void _showWorkflowProfileTooltip(
      GlobalKey titleKey, Map<String, dynamic> workflow) {
    // Don't show if another tooltip is active
    if (_activeTooltipType != null && _activeTooltipType != 'workflow') {
      return;
    }

    // Don't show tooltip if this workflow is already selected and side panel is visible
    if (showSidePanel &&
        selectedWorkflow != null &&
        selectedWorkflow!['id'] == workflow['id']) {
      return;
    }

    // Don't show tooltip if the user is clicking (this is a workaround for the hover/click issue)
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideAllTooltips();

    // Get the position of the workflow title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);

    // Create the overlay entry
    _workflowTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        // Position just below the workflow title
        top: position.dy + 20,
        child: Material(
          color: Colors.transparent,
          child: WorkflowProfileCard(
            id: workflow['id'] ?? '',
            version: workflow['version'] ?? '',
            createdBy: workflow['createdBy'] ?? '',
            createdDate: workflow['createdDate'] ?? '',
            modifiedBy: workflow['modifiedBy'] ?? '',
            modifiedDate: workflow['modifiedDate'] ?? '',
            title: workflow['mainTitle'] ?? workflow['title'] ?? '',
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_workflowTooltipOverlay!);
    _activeTooltipType = 'workflow';
  }

  // Hide workflow profile tooltip
  void _hideWorkflowProfileTooltip() {
    _workflowTooltipOverlay?.remove();
    _workflowTooltipOverlay = null;
    if (_activeTooltipType == 'workflow') {
      _activeTooltipType = null;
    }
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Hide all tooltips
  void _hideAllTooltips() {
    hideProfileTooltip();
    _hideWorkflowProfileTooltip();
    _activeTooltipType = null;
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Show profile card tooltip positioned above the entity title
  void showProfileTooltip(GlobalKey titleKey, Entity entity) {
    // Don't show if another tooltip is active
    if (_activeTooltipType != null && _activeTooltipType != 'profile') {
      return;
    }

    // Don't show tooltip if this entity is already selected and side panel is visible
    if (showSidePanel &&
        selectedEntity != null &&
        selectedEntity!.id == entity.id) {
      return;
    }

    // Don't show tooltip if the user is clicking (this is a workaround for the hover/click issue)
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideAllTooltips();

    // Get the position of the entity title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);

    // Create the overlay entry
    _profileTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        // Position above the entity title with a small offset
        top: position.dy + 20, // Position just above the entity title
        child: Material(
          color: Colors.transparent,
          child: EntityProfileCard(
            id: entity.id ?? '',
            version: entity.version ?? '',
            createdBy: entity.createdBy ?? '',
            createdDate: _parseDate(entity.createdDate),
            modifiedBy: entity.modifiedBy ?? '',
            modifiedDate: _parseDate(entity.modifiedDate),
            title: entity.title ?? '',
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_profileTooltipOverlay!);
    _activeTooltipType = 'profile';
  }

  // Variable to track chat history loading state
  bool isLoadingChatHistory = false;

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      final current = _chatScrollController.offset;
      final max = _chatScrollController.position.maxScrollExtent;
      print("Current: $current, Max: $max");
      // _chatScrollController.animateTo(
      //   max - 148,
      //   duration: const Duration(milliseconds: 300),
      //   curve: Curves.easeOut,
      // );
      // Only scroll if we're not already near the bottom
      const threshold = 00.0; // you can adjust this
      if ((max - current).abs() > threshold) {
        _chatScrollController.animateTo(
          max,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    }
  }

  // Load chat history
  Future<void> loadChatHistory() async {
    setState(() {
      isLoadingChatHistory = true;
    });

    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    // await provider.fetchChatHistory(); // Using AuthService.getUserId()

    setState(() {
      isLoadingChatHistory = false;
    });
  }

  // Cancel the current request
  void _cancelRequest() async {
    // Get the provider before any async operations
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    // If recording, stop it and update audio loading state
    if (isRecording) {
      await _multimediaService.stopSpeechRecognition();
      Logger.info('Speech recognition stopped by cancel request');
      setState(() {
        isAudioLoading = false;
        isRecording = false;
      });
    }

    // Check if widget is still mounted after async operations
    if (!mounted) return;

    // Update state using the provider and local state
    provider.isLoading = false;

    // Add a message to indicate the request was cancelled
    provider.addMessage(ChatMessage(
      content: 'Request cancelled by user.',
      isUser: false,
    ));

    // Update local general loading state
    setState(() {
      isLoading = false;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToBottom();
      }
    });
  }

  // Extract entities data from API response
  EntitiesData _extractEntitiesDataFromResponse(ResultEntity resultEntity) {
    try {
      // Create a new EntitiesData object
      EntitiesData entitiesData = EntitiesData();

      // Create entity groups list
      List<EntityGroup> entityGroups = [];

      if (resultEntity.versions != null && resultEntity.versions!.isNotEmpty) {
        // Process each version
        for (var version in resultEntity.versions!) {
          // Create a new entity group
          EntityGroup entityGroup = EntityGroup(
            id: "group_${version.versionId ?? DateTime.now().millisecondsSinceEpoch.toString()}",
            title: version.data?.firstOrNull?.data?.firstOrNull?.entities
                    ?.firstOrNull?.title ??
                "Entity Group",
            documentId: version.versionId,
            coreCount: "${version.data?.length ?? 0} Core",
            enitiyVersions: version.summary?.versions
                    ?.map((v) => v.versionId ?? "")
                    .toList() ??
                [],
            checked: false,
            entities: [],
          );

          // Process data in the version
          if (version.data != null) {
            List<Entity> entities = [];

            for (var datum in version.data!) {
              if (datum.data != null) {
                for (var datumData in datum.data!) {
                  if (datumData.entities != null) {
                    for (var entityElement in datumData.entities!) {
                      // Store the EntityElement with sections data for later use
                      if (entityElement.id != null) {
                        globalEntityElements[entityElement.id!] = entityElement;
                      }

                      // Convert EntityElement to Entity
                      Entity entity = Entity(
                        id: entityElement.id,
                        title: entityElement.title,
                        description: entityElement.description,
                        version: entityElement.version,
                        expanded: entityElement.expanded,
                        checked: false,
                        attributeString: entityElement.attributeString,
                        createdBy: entityElement.createdBy,
                        createdDate: entityElement.createdDate != null
                            ? _parseDate(entityElement.createdDate!)
                            : null,
                        modifiedBy: entityElement.modifiedBy,
                        modifiedDate: entityElement.modifiedDate,
                        attributes: entityElement.attributes
                            ?.map((attr) => Attribute(
                                  name: attr.name,
                                  type: attr.type,
                                  required: attr.required,
                                  isPk: attr.isPk,
                                  description: attr.description,
                                  isFk: attr.isFk,
                                ))
                            .toList(),
                        businessRules: [],
                        relationType: entityElement.relationType,
                        parentEntity: entityElement.parentEntity,
                      );

                      entities.add(entity);
                    }
                  }
                }
              }
            }

            // Add entities to the group
            entityGroup = entityGroup.copyWith(entities: entities);
          }

          // Add the entity group to the list
          entityGroups.add(entityGroup);
        }
      }

      // Create system info
      SystemInfo systemInfo = SystemInfo(
        enitiyVersions:
            resultEntity.versions?.map((v) => v.versionId ?? "").toList() ?? [],
        entityName:
            resultEntity.versions?.firstOrNull?.summary?.title ?? "Entity Data",
        entityCount: resultEntity.versions
                ?.fold<int>(0, (sum, v) => sum + (v.data?.length ?? 0)) ??
            0,
        coreEntities: resultEntity.versions?.length ?? 0,
        headerText: "There are total 5 Object, 4 Core and 1 Transaction Object",
        bulletPoints: resultEntity.versions
                ?.map((v) =>
                    "${v.versionName ?? v.versionId} has ${v.data?.length ?? 0} entities")
                .toList() ??
            [],
      );

      // Update the EntitiesData object
      entitiesData = entitiesData.copyWith(
        entityGroups: entityGroups,
        systemInfo: systemInfo,
      );

      // Initialize selected versions
      entitiesData.initializeSelectedVersions();

      return entitiesData;
    } catch (e) {
      Logger.error('Error extracting entities data from response: $e');
      // Return empty EntitiesData in case of error
      return EntitiesData();
    }
  }

  // This method has been moved to WorkflowDataProvider

  // Update workflow data from API response
  Future<void> _updateWorkflowsFromApiResponse(ConversationResponse response,
      WebHomeProviderStatic webHomeProvider) async {
    try {
      // Extract workflow data directly from the response
      List<Map<String, dynamic>> extractedWorkflows = [];
      List<String> extractedVersions = [];

      // Check if we have agent results with workflow data
      if (response.workflowResult?.agentResults != null) {
        for (var agent in response.workflowResult!.agentResults!) {
          if (agent.result?.workFlow?.workFlowDetails != null) {
            final workflowDetails = agent.result!.workFlow!.workFlowDetails!;

            // Extract version from workflow details
            if (workflowDetails.version != null &&
                workflowDetails.version!.isNotEmpty) {
              extractedVersions.add(workflowDetails.version!);
            }

            // Create a workflow map from the API data
            Map<String, dynamic> workflow = {
              'id': workflowDetails.id ??
                  'workflow_${DateTime.now().millisecondsSinceEpoch}',
              'title': workflowDetails.title ?? 'Untitled Workflow',
              'description': workflowDetails.description ?? '',
              'version': workflowDetails.version ?? '1.0',
              'createdBy': workflowDetails.createdBy ?? '',
              'createdDate': workflowDetails.createdDate ?? '',
              'modifiedBy': workflowDetails.modifiedBy ?? '',
              'modifiedDate': workflowDetails.modifiedDate ?? '',
              'mainTitle': workflowDetails.mainTitle ??
                  workflowDetails.title ??
                  'Untitled Workflow',
              'tree': _convertApiTreeToJsonFormat(workflowDetails.tree ?? []),
              'workFlowDetails': workflowDetails,
            };

            extractedWorkflows.add(workflow);
          }

          // Also check for versions_list in the workflow result
          if (agent.result?.workFlow?.versionsList != null) {
            for (var versionInfo in agent.result!.workFlow!.versionsList!) {
              if (versionInfo.versionName != null &&
                  versionInfo.versionName!.isNotEmpty) {
                extractedVersions.add(versionInfo.versionName!);
              }
            }
          }
        }
      }

      if (extractedWorkflows.isNotEmpty) {
        // Update our local workflows list directly
        workflows = extractedWorkflows;

        // Store workflow versions and system info
        workflowVersions =
            extractedVersions.toSet().toList(); // Remove duplicates
        workflowSystemInfo = {
          'workflowCount': extractedWorkflows.length,
          'systemVersions': workflowVersions,
          'selectedVersion':
              workflowVersions.isNotEmpty ? workflowVersions.first : null,
          'bulletPoints': [
            'Workflow data extracted from API response',
            'Contains ${extractedWorkflows.length} workflows',
            'Available versions: ${workflowVersions.join(', ')}'
          ],
        };

        // Set selected workflow version if not already set
        if (selectedWorkflowVersion == null && workflowVersions.isNotEmpty) {
          selectedWorkflowVersion = workflowVersions.first;
        }

        // Log detailed information about the extracted workflows
        Logger.info(
            '🔍 API SUCCESS: Extracted ${extractedWorkflows.length} workflows from API response');
        Logger.info(
            '🔍 API WORKFLOW VERSIONS: Found ${workflowVersions.length} versions: ${workflowVersions.join(', ')}');
        if (extractedWorkflows.isNotEmpty) {
          Logger.info(
              '🔍 API WORKFLOW DETAILS: First workflow title: "${extractedWorkflows[0]['title']}"');
          Logger.info(
              '🔍 API WORKFLOW DETAILS: First workflow ID: "${extractedWorkflows[0]['id']}"');
          Logger.info(
              '🔍 API WORKFLOW DETAILS: Tree nodes count: ${(extractedWorkflows[0]['tree'] as List).length}');
        }

        // Update the provider with our extracted data
        try {
          final workflowDataProvider =
              Provider.of<WorkflowDataProvider>(context, listen: false);
          await Future.microtask(() {
            workflowDataProvider.updateDirectly(extractedWorkflows);
            Logger.info(
                '🔍 PROVIDER UPDATE: Successfully updated WorkflowDataProvider with API data');
          });
        } catch (providerError) {
          // Log the error but continue with direct data
          Logger.error('Provider error (non-fatal): $providerError');
          Logger.info(
              '🔍 DIRECT DATA: Continuing with direct workflow data from API');
        }

        Logger.info(
            '🔍 API COMPLETE: Successfully updated workflows from API response: ${extractedWorkflows.length} workflows');
      } else {
        // If no workflows were found, log it and throw an exception
        Logger.info('🔍 API EMPTY: No workflow data found in the response');
        throw Exception('No workflow data found in API response');
      }
    } catch (e) {
      // Log the error but don't add an error message to the chat
      // The calling code will handle showing appropriate messages
      Logger.error('Error in _updateWorkflowsFromApiResponse: $e');
      rethrow; // Rethrow so the calling code can handle it
    }
  }

  // Helper method to convert API tree format to JSON format
  List<Map<String, dynamic>> _convertApiTreeToJsonFormat(
      List<dynamic> apiTree) {
    List<Map<String, dynamic>> result = [];

    for (var node in apiTree) {
      Map<String, dynamic> treeNode = {
        'id': node.id ?? 'node_${DateTime.now().millisecondsSinceEpoch}',
        'text': node.text ?? '',
        'level': node.level ?? 0,
        'sequence': node.sequence ?? 0,
        'altText': node.altText,
        'isRejection': node.isRejection ?? false,
        'isApproval': node.isApproval ?? false,
        'isParallel': node.isParallel ?? false,
        'hasCheckmark': node.hasCheckmark,
        'hasX': node.hasX,
      };

      // Add children if they exist
      if (node.children != null && node.children!.isNotEmpty) {
        treeNode['children'] = _convertApiChildrenToJsonFormat(node.children!);
      }

      result.add(treeNode);
    }

    return result;
  }

  // Helper method to convert API children to JSON format
  List<Map<String, dynamic>> _convertApiChildrenToJsonFormat(
      List<dynamic> apiChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in apiChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'child_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasX': child.hasX,
      };

      // Add nested children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] =
            _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }

  // Helper method to convert nested children to JSON format
  List<Map<String, dynamic>> _convertNestedChildrenToJsonFormat(
      List<dynamic> nestedChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in nestedChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'nested_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasCheckmark': child.hasCheckmark,
      };

      // Handle recursive children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] =
            _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }

  // We no longer preload workflow data - it's loaded directly from API responses

  // Build workflows response using direct data (not from provider)
  Widget _buildWorkflowsResponseDirect() {
    // Show loading indicator if no workflows are available
    if (workflows.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading workflows data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Get the first workflow from the list
    final workflow = workflows[0];

    // Get the tree data from the workflow
    final List<dynamic> treeData = workflow['tree'] ?? [];
    final String mainTitle = workflow['mainTitle'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with workflow info
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Expanded(
            //   child: Consumer<AuthProvider>(
            //     builder: (context, authProvider, _) {
            //       // Get the username from the user profile
            //       final String firstName = authProvider.user?.username ?? '';

            //       // Get the localized greeting text
            //       final String greeting = AppLocalizations.of(context)
            //           .translate('build.workflowGreeting');

            //       // If we have a username, prepend it to the greeting
            //       final String displayText =
            //           firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

            //       return Text(
            //         displayText,
            //         style: TextStyle(
            //           fontFamily: 'TiemposText',
            //           fontSize: 14,
            //           fontWeight: FontWeight.w500,
            //         ),
            //       );
            //     },
            //   ),
            // ),
            // SvgPicture.asset('assets/images/eye.svg', height: 14, width: 18),
            // SizedBox(width: 5),
            // Text(
            //   'PREVIEW UI',
            //   style: TextStyle(
            //     fontSize: 9,
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),
            // SizedBox(width: 10),
            SvgPicture.asset('assets/images/icons/box.svg',
                height: 24, width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.md),

        // Performance & Monitoring Framework section
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xffB4B4B4), width: 0.5),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                decoration: BoxDecoration(
                  color: Color(0xffF8F9FA),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Text(
                  "Performance & Monitoring Framework",
                  style: FontManager.getCustomStyle(
                      color: AppColors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleSmall(context)),
                ),
              ),

              // Performance and monitoring components
              Padding(
                padding: EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    _buildManagementCategory(
                      title: "Application Performance Monitoring",
                      description:
                          "Real-time monitoring of application performance metrics and user experience",
                      pending: "In Progress",
                    ),
                    _buildManagementCategory(
                        title: "System Health Monitoring",
                        description:
                            "Infrastructure monitoring including CPU, memory, and disk usage",
                        pending: "Completed"),
                    _buildManagementCategory(
                        title: "Error Tracking & Logging",
                        description:
                            "Centralized error tracking and comprehensive application logging",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "Performance Analytics",
                        description:
                            "Analytics dashboard for performance trends and bottleneck identification",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Load Testing Framework",
                        description:
                            "Automated load testing and performance benchmarking tools",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "Database Performance Monitoring",
                        description:
                            "Query optimization and database performance tracking",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "User Experience Monitoring",
                        description:
                            "Frontend performance monitoring and user interaction tracking",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "Alert Management System",
                        description:
                            "Automated alerting for performance issues and system failures",
                        isLast: true,
                        pending: "Completed"),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppSpacing.md),

        // User Experience & Interface Design section
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xffB4B4B4), width: 0.5),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                decoration: BoxDecoration(
                  color: Color(0xffF8F9FA),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Text(
                  "User Experience & Interface Design",
                  style: FontManager.getCustomStyle(
                      color: AppColors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleSmall(context)),
                ),
              ),

              // User experience components
              Padding(
                padding: EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    _buildManagementCategory(
                      title: "Responsive Design Framework",
                      description:
                          "Mobile-first design approach with adaptive layouts for all devices",
                      pending: "In Progress",
                    ),
                    _buildManagementCategory(
                        title: "User Interface Components",
                        description:
                            "Reusable UI components library with consistent design patterns",
                        pending: "Completed"),
                    _buildManagementCategory(
                        title: "Accessibility Standards",
                        description:
                            "WCAG 2.1 compliance and inclusive design principles",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "User Journey Mapping",
                        description:
                            "Comprehensive user flow analysis and optimization",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Interactive Prototyping",
                        description:
                            "High-fidelity prototypes for user testing and validation",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "Design System",
                        description:
                            "Unified design language with style guides and component library",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Usability Testing",
                        description:
                            "User testing sessions and feedback integration processes",
                        pending: "In Progress"),
                    _buildManagementCategory(
                        title: "Cross-Platform Compatibility",
                        description:
                            "Consistent user experience across web, mobile, and desktop platforms",
                        isLast: true,
                        pending: "Completed"),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppSpacing.md),

        // Security & Compliance Framework
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            color: Colors.white,
          ),
          padding: EdgeInsets.all(AppSpacing.xxs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workflow header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSpacing.xs),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Expanded(
                    //   child: Text(
                    //     "Workflow",
                    //     style: TextStyle(
                    //       fontFamily: 'TiemposText',
                    //       fontSize: 12.0,
                    //       color: Colors.black,
                    //     ),
                    //     overflow: TextOverflow.ellipsis,
                    //   ),
                    // ),

                    // Custom dropdown for workflow versions from API
                    workflowSystemInfo['systemVersions'] != null &&
                            (workflowSystemInfo['systemVersions'] as List)
                                .isNotEmpty
                        ? CustomEntityVersionDropdown(
                            versions: (workflowSystemInfo['systemVersions']
                                    as List<String>)
                                .map((e) => e.toString())
                                .toList(),
                            selectedVersion:
                                workflowSystemInfo['selectedVersion'] ?? '',
                            onVersionSelected: (String version) {
                              setState(() {
                                workflowSystemInfo['selectedVersion'] = version;
                                selectedWorkflowVersion = version;
                              });
                            },
                          )
                        : Container(),

                    SizedBox(width: AppSpacing.xs),

                    // Workflow count display similar to agent count
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: AppSpacing.sm, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100,
                        borderRadius: BorderRadius.circular(AppSpacing.xxs),
                      ),
                      child: Text(
                        '${workflowSystemInfo['workflowCount'] ?? 0} Workflow',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'TiemposText',
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              // Workflow title
              Padding(
                padding: EdgeInsets.only(left: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Builder(builder: (context) {
                      final GlobalKey titleKey = GlobalKey(
                          debugLabel: 'workflowTitle_${workflow['id']}');

                      return MouseRegion(
                        cursor: SystemMouseCursors.click,
                        onEnter: (_) {
                          // Show workflow profile tooltip when hovering on title
                          _showWorkflowProfileTooltip(titleKey, workflow);
                        },
                        onExit: (_) {
                          // Hide profile tooltip when not hovering
                          _hideWorkflowProfileTooltip();
                        },
                        child: InkWell(
                          onTap: () {
                            // Update last click time to prevent tooltip from showing
                            _lastClickTime = DateTime.now();

                            // Hide any existing tooltip when clicking
                            _hideWorkflowProfileTooltip();

                            setState(() {
                              selectedRole = null;
                              selectedEntity = null;
                              selectedWorkflow = workflow;
                              showSidePanel = true;

                              // Scroll the workflow tree horizontally to show content that might be overflowing
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (_workflowTreeScrollController.hasClients) {
                                  _workflowTreeScrollController.animateTo(
                                    0, // Scroll to position 0 (leftmost content)
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.easeInOut,
                                  );
                                }
                              });
                            });
                          },
                          child: Text(
                            mainTitle,
                            key: titleKey,
                            style: TextStyle(
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.md),

              // Workflow tree visualization using WorkflowTreeBuilder
              Padding(
                padding: EdgeInsets.all(AppSpacing.xs),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: _workflowTreeScrollController,
                  child: WorkflowTreeBuilder.buildWorkflowTree(treeData),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
      ],
    );
  }

  Widget chatHistory() {
    final provider = Provider.of<WebHomeProviderStatic>(context);
    final chatHistoryList = provider.chatHistory;

    return AnimatedContainer(
      duration: Duration(milliseconds: 600),
      curve: Curves.easeInOut,
      width: provider.isChatHistoryExpanded
          ? MediaQuery.of(context).size.width / 6
          : 0,
      decoration: BoxDecoration(
        color: Colors.white,
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withValues(alpha: 0.13), // 0.05 * 255 ≈ 13
        //     blurRadius: 5,
        //     offset: Offset(0, 3),
        //   ),
        // ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.only(
                left: AppSpacing.xs,
                right: AppSpacing.xs,
                bottom: AppSpacing.xxs,
                top: AppSpacing.xs),
            decoration: BoxDecoration(
                border: Border(
                    bottom:
                        BorderSide(color: Colors.grey.shade300, width: 0.5))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min, // Set to min to prevent overflow
              children: [
                provider.isChatHistoryExpanded
                    ? Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            "Chat History",
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 12,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      )
                    : Container(),
                // Wrap for the buttons to prevent overflow
                Wrap(
                  spacing: AppSpacing.xs,
                  children: [
                    // Add clickable image that only shows when chat has been submitted
                    if (messages
                        .isNotEmpty) // Only show if there are chat messages
                      _HoverAddButton(
                        onTap: () {
                          // Navigate to home screen to start a new chat
                          provider.isChatHistoryExpanded =
                              !provider.isChatHistoryExpanded;
                          provider.currentScreenIndex = ScreenConstants.home;
                          // Reset the conversation
                          provider.resetConversation();
                        },
                      ),
                    _HoverExpandButton(
                      isExpanded: provider.isChatHistoryExpanded,
                      onTap: () async {
                        // Toggle chat history expanded state
                        provider.isChatHistoryExpanded =
                            !provider.isChatHistoryExpanded;
                        // Also refresh chat history when expanding
                        if (provider.isChatHistoryExpanded) {
                          await loadChatHistory();
                        }
                      },
                      tooltipMessage: provider.isChatHistoryExpanded
                          ? 'Collapse'
                          : 'Expand',
                    ),
                  ],
                ),
              ],
            ),
          ),
          // SizedBox(
          //   height: AppSpacing.xxs,
          // ),

          // Chat history list - only show when expanded
          if (provider.isChatHistoryExpanded)
            Expanded(
              child: isLoadingChatHistory
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            context.tr('home.loadingChatHistory'),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    )
                  : chatHistoryList.isEmpty
                      ? Center(
                          child: Text(
                            context.tr('home.noChatHistory'),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        )
                      : Builder(
                          builder: (context) {
                            // Filter chat history items to only include those with conversation_id
                            final filteredChatHistory = chatHistoryList
                                .where((chat) => chat["session_id"] != null)
                                .toList();

                            // Log the filtering results
                            // Logger.info(
                            //     'Chat history filtering: ${chatHistoryList.length} total items, ${filteredChatHistory.length} with conversation_id');
                            // for (var chat in chatHistoryList) {
                            //   Logger.info(
                            //       'Chat item: id=${chat['id']}, conversation_id=${chat['conversation_id']}, title=${chat['title']}');
                            // }

                            return filteredChatHistory.isEmpty
                                ? Center(
                                    child: Text(
                                      context.tr('home.noChatHistory'),
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: filteredChatHistory.length,
                                    itemBuilder: (context, index) {
                                      final chat = filteredChatHistory[index];
                                      return _buildChatHistoryItem(chat);
                                    },
                                  );
                          },
                        ),
            ),
        ],
      ),
    );
  }

  // Build a chat history item
  Widget _buildChatHistoryItem(Map<String, dynamic> chat) {
    // Skip items without conversation_id (double check)
    if (chat['session_id'] == null) {
      Logger.info(
          'Skipping chat history item without conversation_id: ${chat['id']}');
      return SizedBox.shrink();
    }

    // Background color
    final backgroundColor = Colors.white;

    // Determine chat type based on title
    final title = chat['title']?.toString().toLowerCase() ?? '';
    String chatType = 'General'; // Default

    if (title.contains('general')) {
      chatType = 'General';
    } else if (title.contains('internet')) {
      chatType = 'Internet';
    } else if (title.contains('nsl')) {
      chatType = 'NSL';
    }

    // Create a stateful widget for hover effects
    return _ChatHistoryItemWidget(
      chat: chat,
      backgroundColor: backgroundColor,
      // chatTypeIcon: chatTypeIcon,
      isExpanded:
          Provider.of<WebHomeProviderStatic>(context).isChatHistoryExpanded,
      onTap: () {
        // Load this session
        final provider =
            Provider.of<WebHomeProviderStatic>(context, listen: false);
        final sessionId = chat['session_id']?.toString();

        if (sessionId != null) {
          // Show chat session loading indicator
          setState(() {
            isLoadingChatSession = true;
            provider.clearMessages();
          });

          // Load the session and fetch its history using the new API
          provider.fetchConversationHistory(sessionId).then((result) {
            if (result['success']) {
              // Debug the entire API response
              Logger.info('Full API response: $result');

              // Process the chat history from new API structure
              final chatHistory = result['data']['history'] as List<dynamic>?;

              // Debug the chat history
              Logger.info('Chat history response: $chatHistory');

              // Extract mode from the first message if available
              String? extractedMode;
              if (chatHistory != null && chatHistory.isNotEmpty) {
                // Try to extract mode from the first message or session metadata
                final firstMessage = chatHistory.first;
                extractedMode = firstMessage['mode'] as String?;

                // If mode is not in the message, try to infer from the session data
                if (extractedMode == null) {
                  final sessionData = result['data'];
                  extractedMode = sessionData['mode'] as String?;
                }

                // If still no mode found, try to get it from the session title or default to first available mode
                if (extractedMode == null) {
                  final title = chat['title']?.toString().toLowerCase() ?? '';
                  if (title.contains('nsl')) {
                    extractedMode = 'nsl_expert';
                  } else if (title.contains('business')) {
                    extractedMode = 'business_assistant';
                  } else if (provider.availableModes.isNotEmpty) {
                    extractedMode = provider.availableModes.first.id;
                  }
                }
              }

              setState(() {
                isLoadingChatSession = false;
                provider.clearMessages(); // Clear existing messages first

                if (chatHistory != null && chatHistory.isNotEmpty) {
                  // Add messages from the chat history using new API structure
                  for (var historyItem in chatHistory) {
                    // Log the entire message object to understand its structure
                    Logger.info('History item: $historyItem');

                    // Add user message
                    final userMessage = historyItem['message'] ?? '';
                    if (userMessage.isNotEmpty) {
                      provider.addMessage(ChatMessage(
                        content: userMessage,
                        isUser: true,
                      ));
                    }

                    // Add AI response
                    final aiResponse = historyItem['response'] ?? '';
                    if (aiResponse.isNotEmpty) {
                      provider.addMessage(ChatMessage(
                        content: aiResponse,
                        isUser: false,
                        isTypingComplete: true,
                      ));
                    }

                    Logger.info(
                        'Added message pair: User - $userMessage, AI - $aiResponse');
                  }

                  // Set the mode session ID to continue conversation with the existing session
                  provider.setModeSessionForContinuation(
                      sessionId, extractedMode);

                  // Set the selected quick message based on extracted mode
                  if (extractedMode != null) {
                    final modeName = provider.getModeNameById(extractedMode);
                    if (modeName != null) {
                      provider.selectedQuickMessage = modeName;
                      Logger.info('Set selected quick message to: $modeName');
                    }
                  }

                  // Force UI to update
                  if (messages.isEmpty) {
                    provider.addMessage(ChatMessage(
                      content:
                          'No chat history available for ${chat['title'] ?? 'Untitled'}',
                      isUser: false,
                    ));
                  }
                } else {
                  // Add a system message if no history is available
                  provider.addMessage(ChatMessage(
                    content:
                        'No chat history available for ${chat['title'] ?? 'Untitled'}',
                    isUser: false,
                  ));
                  // Still set the session ID for continuing the conversation
                  provider.setModeSessionForContinuation(
                      sessionId, extractedMode);

                  // Set the selected quick message based on extracted mode
                  if (extractedMode != null) {
                    final modeName = provider.getModeNameById(extractedMode);
                    if (modeName != null) {
                      provider.selectedQuickMessage = modeName;
                      Logger.info('Set selected quick message to: $modeName');
                    }
                  }
                }
              });

              // Debug the messages list after setState using microtask to ensure setState is processed
              Future.microtask(() {
                Logger.info(
                    'Messages list after setState: ${messages.length} messages');
                for (int i = 0; i < messages.length; i++) {
                  Logger.info('Message $i: ${messages[i].content}');
                }
              });
            } else {
              // Show error message
              setState(() {
                isLoadingChatSession = false;
                provider.addMessage(ChatMessage(
                  content: 'Error loading conversation: ${result['message']}',
                  isUser: false,
                ));
              });

              // Debug error message
              Future.microtask(() {
                Logger.info('Error message added: ${messages.length} messages');
                if (messages.isNotEmpty) {
                  Logger.info('Error message: ${messages.last.content}');
                }
              });
            }
          }).catchError((error) {
            // Handle error
            setState(() {
              isLoadingChatSession = false;
              provider.addMessage(ChatMessage(
                content: 'Error: $error',
                isUser: false,
              ));
            });

            // Debug catch error message
            Future.microtask(() {
              Logger.info(
                  'Catch error message added: ${messages.length} messages');
              if (messages.isNotEmpty) {
                Logger.info('Catch error message: ${messages.last.content}');
              }
            });
          });
        }
      },
      formatTimestamp: _formatTimestamp,
    );
  }

  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return '';

    try {
      // Try to parse the timestamp
      DateTime dateTime;
      if (timestamp is String) {
        if (timestamp.contains('T')) {
          dateTime = DateTime.parse(timestamp);
        } else {
          // If it's a Unix timestamp as string
          dateTime =
              DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp) * 1000);
        }
      } else if (timestamp is int) {
        // If it's a Unix timestamp as int
        dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      } else if (timestamp is double) {
        // If it's a Unix timestamp as double
        dateTime =
            DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      } else {
        return '';
      }

      // Format the date
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        // Today - show time
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays == 1) {
        // Yesterday
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        // This week - show day name
        final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        return dayNames[dateTime.weekday - 1];
      } else {
        // Older - show date
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return '';
    }
  }

  // Handle the conversation flow
  Future<void> _handleConversation(String text) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    // Check if this is a Solution quick message
    if (provider.selectedQuickMessage == "Solution") {
      // Use the new Solution API
      if (provider.currentSessionId != null) {
        // Use existing session ID for subsequent messages
        await _sendSolutionMessage(text);
      } else {
        // Create a new solution session for the first message
        await _createNewSolutionSession(text);
      }
    } else {
      // Use the existing conversation flow for other message types
      if (provider.currentSessionId != null) {
        // Use the existing session ID
        await _sendConversationInput(provider.currentSessionId!, text);
      } else {
        // Create a new session
        _createNewSession(text);
      }
    }
  }

  // Create a new conversation session
  Future<void> _createNewSession(String text) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    try {
      final response = await provider.createNewConversationSession();

      if (!response['success']) {
        setState(() {
          isLoading = false;
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${response['message']}',
            isUser: false,
          ));
        });
      } else {
        // If successful, call the conversation API with the session ID and user input
        // Note: We don't set isLoading = false here because we're making another API call
        final sessionId = response['session_id'];
        await _sendConversationInput(sessionId, text);
      }

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (error) {
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Create a new solution session for the first message (streaming)
  Future<void> _createNewSolutionSession(String text) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    try {
      String? thinkingMessage;
      // Listen to the streaming response
      await for (final event in provider.createSolutionSession(text)) {
        if (!mounted) break; // Check if widget is still mounted

        if (event['success'] == true) {
          final eventType = event['event_type'] as String?;
          final eventData = event['data'] as Map<String, dynamic>?;

          // Handle different event types
          switch (eventType) {
            case 'start':
              // Session started - could show a starting message
              Logger.info('Solution session started');
              break;

            case 'thinking':
              // AI is thinking - could show thinking indicator
              final thinkingData = eventData?['data'] as Map<String, dynamic>?;
              thinkingMessage = thinkingData?['thinking'] as String?;
              if (thinkingMessage != null) {
                Logger.info('AI thinking: $thinkingMessage');
              }
              break;

            case 'progress':
              // Progress update - could show progress indicator
              final progressData = eventData?['data'] as Map<String, dynamic>?;
              final progress = progressData?['progress'] as num?;
              final message = progressData?['message'] as String?;
              if (message != null && progress != null) {
                Logger.info('Progress: $message ($progress%)');
              }
              break;

            case 'question':
              // Question received - show it to the user
              final questionData = eventData?['data'] as Map<String, dynamic>?;
              final question = questionData?['question'] as String?;

              if (question != null) {
                setState(() {
                  isLoading = false;
                  provider.addMessage(ChatMessage(
                    content: question,
                    isUser: false,
                    reasoningData: [thinkingMessage ?? ''],
                    isReasoningDataExpanded: true,
                    // You can add additional data if needed
                  ));
                });

                // Scroll to bottom after the UI updates
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _scrollToBottom();
                });
              }
              break;

            case 'completion':
              // Session completed - now call getSolutionStatus
              Logger.info('Solution session completed');
              setState(() {
                isLoading = false;
              });

              // Call getSolutionStatus after completion
              await provider.getSolutionStatus();
              break;

            default:
              Logger.info('Received unknown event type: $eventType');
          }
        } else {
          // Handle error events
          setState(() {
            isLoading = false;
            provider.addMessage(ChatMessage(
              content: 'Error: ${event['message']}',
              isUser: false,
            ));
          });

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
          break; // Exit the stream on error
        }
      }
    } catch (error) {
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Create a new solution message (streaming)
  Future<void> _sendSolutionMessage(String text) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    try {
      String? thinkingMessage = "";
      provider.addMessage(ChatMessage(
        content: "Lorem ipusm",
        isUser: false,
        reasoningData: ["Lorem ipusm" ?? ''],
        isReasoningDataExpanded: true,
        brdDocument: SolutionSessionModel(
          updatedNodes: [
            UpdatedNode(
              nodeId: "1",
              section: "1",
              key: "1",
              prompt: "1",
              value: "1",
              weight: 1,
              isMandatory: true,
              updatedAt: DateTime.now(),
            ),
          ],
        ),
      ));
      setState(() {});
      //   // Listen to the streaming response
      //   await for (final event in provider.createSolutionMessage(text)) {
      //     if (!mounted) break; // Check if widget is still mounted

      //     if (event['success'] == true) {
      //       final eventType = event['event_type'] as String?;
      //       final eventData = event['data'] as Map<String, dynamic>?;

      //       // Handle different event types
      //       switch (eventType) {
      //         case 'start':
      //           // Session started - could show a starting message
      //           Logger.info('Solution message started');
      //           break;

      //         case 'thinking':
      //           // AI is thinking - could show thinking indicator
      //           final thinkingData = eventData?['data'] as Map<String, dynamic>?;
      //           thinkingMessage = thinkingData?['thinking'] as String?;
      //           if (thinkingMessage != null) {
      //             Logger.info('AI thinking: $thinkingMessage');
      //           }
      //           break;

      //         case 'progress':
      //           // Progress update - could show progress indicator
      //           final progressData = eventData?['data'] as Map<String, dynamic>?;
      //           final progress = progressData?['progress'] as num?;
      //           final message = progressData?['message'] as String?;
      //           if (message != null && progress != null) {
      //             Logger.info('Progress: $message ($progress%)');
      //           }
      //           break;

      //         case 'question':
      //           // Question received - show it to the user
      //           final questionData = eventData?['data'] as Map<String, dynamic>?;
      //           final question = questionData?['question'] as String?;

      //           if (question != null) {
      //             // Process the question text to format it properly
      //             final processedQuestion = _processQuestionText(question);

      //             setState(() {
      //               isLoading = false;
      //               provider.addMessage(ChatMessage(
      //                   content: question,
      //                   isUser: false,
      //                   reasoningData: [thinkingMessage ?? ''],
      //                   isReasoningDataExpanded: true
      //                   // brdDocument: provider.solutionSessionModel,
      //                   ));
      //             });

      //             // Scroll to bottom after the UI updates
      //             WidgetsBinding.instance.addPostFrameCallback((_) {
      //               _scrollToBottom();
      //             });
      //           }
      //           break;

      //         case 'completion':
      //           // Session completed - now call getSolutionStatus

      //           break;

      //         default:
      //           Logger.info('Received unknown event type: $eventType');
      //       }
      //     } else {
      //       // Handle error events
      //       setState(() {
      //         isLoading = false;
      //         provider.addMessage(ChatMessage(
      //           content: 'Error: ${event['message']}',
      //           isUser: false,
      //         ));
      //       });

      //       // Scroll to bottom after the UI updates
      //       WidgetsBinding.instance.addPostFrameCallback((_) {
      //         _scrollToBottom();
      //       });
      //       break; // Exit the stream on error
      //     }
      //   }
    } catch (error) {
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
    Logger.info('Solution message completed');
    setState(() {
      isLoading = false;
    });

    // Call getSolutionStatus after completion
    await provider.getSolutionStatus();
  }

  // Send input to an existing conversation
  Future<void> _sendConversationInput(String sessionId, String text) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    // Get the last user message to check for file data
    FileUploadOcrResponse? fileData;
    if (messages.isNotEmpty &&
        messages.last.isUser &&
        messages.last.fileData != null) {
      fileData = messages.last.fileData;
      // Logger.info('Found file data in last message: ${fileData?['fileName']}');
    }

    // Use the lastUserMessageForApi if available, otherwise use the original text
    final String messageToSend = provider.lastUserMessageForApi.isNotEmpty
        ? provider.lastUserMessageForApi
        : text;

    Logger.info('Sending message to API: $messageToSend');

    try {
      //   final localData =
      //       await rootBundle.loadString('assets/data/testing_data.json');

      // final convResponse = {
      //   'success': true,
      //   'data': jsonDecode(localData),
      // };

      final convResponse = await provider
          .sendConversationInput(sessionId, messageToSend, fileData: fileData);

      // Reset the lastUserMessageForApi after sending
      provider.lastUserMessageForApi = '';

      // Process workflow data first if available
      bool hasWorkflowData = false;
      Agent? workflowAgent;
      Agent? entityAgent;

      if (convResponse['success']) {
        final data = convResponse['data'];
        final conversationResponse = ConversationResponse.fromJson(data);

        // Check for workflow data in the response
        if (conversationResponse.workflowResult?.agentResults != null) {
          for (var agent
              in conversationResponse.workflowResult!.agentResults!) {
            if (agent.result?.workFlow?.workFlowDetails != null) {
              hasWorkflowData = true;
            }
            if (agent.agentName == "WORKFLOW") {
              workflowAgent = agent;
            }
            if (agent.agentName == "ENTITY") {
              entityAgent = agent;
            }
          }
        }
      }

      // Now update the UI
      setState(() {
        isLoading = false;

        if (convResponse['success']) {
          // Parse the response using the model
          final data = convResponse['data'];
          final conversationResponse = ConversationResponse.fromJson(data);

          if (conversationResponse.classification != null &&
              conversationResponse.classification!.explanation != null &&
              conversationResponse.workflowResult?.agentResults != null) {
            // Create a combined response for multiple response types
            String combinedContent = "";
            List<Widget> customContentWidgets = [];
            Agent? tenantLayerAgent;
            if (conversationResponse.workflowResult?.agentResults != null) {
              // Log all agent names for debugging
              for (var agent
                  in conversationResponse.workflowResult!.agentResults!) {
                // Check if this is the TENANT_LAYER agent and log its structure
                if (agent.agentName == "TENANT_LAYER") {
                  // Check if result is available
                  if (agent.result != null) {
                    tenantLayerAgent = agent;
                    break;
                  }
                }
              }
            }

            bool hasShownAnyResponse = false;

            // Add explanation content if available
            if (conversationResponse.classification?.explanation != null) {
              combinedContent =
                  conversationResponse.classification!.explanation!;
              hasShownAnyResponse = true;
            }
            // Process tenant_layer agent data if available
            if (tenantLayerAgent != null) {
              List<String?> versions = [];
              if (tenantLayerAgent.result?.agents != null) {
                for (var agent in tenantLayerAgent.result!.agents!) {
                  versions.add(agent.version);
                }
              }

              Logger.info(
                  '🔍 DATA SOURCE CHECK: Found tenant_layer agent, checking for data');

              // Extract agent data from the response - our updated method will handle different data structures
              AgentData extractedAgentData =
                  _extractAgentDataFromResponse(tenantLayerAgent, versions);

              // Check if we extracted any agent data
              if (extractedAgentData.agents != null &&
                  extractedAgentData.agents!.isNotEmpty) {
                // Store the agent data in the global variable
                globalAgentData = extractedAgentData;
                Logger.info(
                    '🔍 DATA SOURCE CHECK: Stored TENANT_LAYER agent data in globalAgentData with ${extractedAgentData.agents!.length} agents');

                // Store the agent data in the global variable
                setState(() {
                  isLoadingRoles = false;
                });

                // Show the roles response with data from the API
                Logger.info(
                    '🔍 DATA SOURCE CHECK: Adding _buildRolesResponse to chat messages');

                // customContentWidgets.add(_buildRolesResponse());
                customContentWidgets.add(_buildRolesResponse());
                hasShownAnyResponse = true;
                hasShownAnyResponse = true;
                Logger.info(
                    '🔍 DATA SOURCE CHECK: Processed TENANT_LAYER agent data and added to chat');
              } else {
                Logger.info(
                    '🔍 DATA SOURCE CHECK: No agent data could be extracted from TENANT_LAYER agent');
              }
            }

            // Process entity data if available
            if (entityAgent != null && entityAgent.result?.entity != null) {
              // Extract entity data from the response
              EntitiesData extractedEntitiesData =
                  _extractEntitiesDataFromResponse(entityAgent.result!.entity!);

              // Update the global entities data
              globalEntitiesData = extractedEntitiesData;

              // Show the entities response
              customContentWidgets.add(_buildEntitiesResponse());
              hasShownAnyResponse = true;
            }

            // If we have workflow data, we'll process it outside setState
            // Just set a flag to indicate we'll process workflow data later
            bool hasWorkflowToProcess = (workflowAgent != null &&
                    workflowAgent.result?.workFlow != null) ||
                hasWorkflowData;

            if (hasWorkflowToProcess) {
              hasShownAnyResponse = true;
            }

            // If we have any response to show
            if (hasShownAnyResponse) {
              // Create a combined message with all response types
              if (customContentWidgets.isNotEmpty || hasWorkflowToProcess) {
                // If we have custom content or will add workflow later,
                // create a message with combined content

                ChatMessage message = ChatMessage(
                  content: combinedContent,
                  isUser: false,
                  // Only add custom content if we have entity data
                  // Workflow data will be added separately outside setState
                  customContent: customContentWidgets.isNotEmpty
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: customContentWidgets,
                        )
                      : null,
                );
                if (fileUploadOcrResponse != null) {
                  message.fileData = fileUploadOcrResponse;
                  fileUploadOcrResponse = null;
                }
                provider.addMessage(message);
              } else {
                // If we only have text content
                ChatMessage message = ChatMessage(
                  content: combinedContent,
                  isUser: false,
                );
                if (fileUploadOcrResponse != null) {
                  message.fileData = fileUploadOcrResponse;
                  fileUploadOcrResponse = null;
                }
                provider.addMessage(message);
              }
            } else {
              fileUploadOcrResponse = null;
              // If no responses were shown, add a generic message
              provider.addMessage(ChatMessage(
                content:
                    "I've processed your request but couldn't find specific data to display.",
                isUser: false,
              ));
            }
          } else if (conversationResponse.classification?.explanation != null) {
            fileUploadOcrResponse = null;
            // Add the explanation to the chat
            provider.addMessage(ChatMessage(
              content: conversationResponse.classification!.explanation!,
              isUser: false,
            ));
          } else {
            fileUploadOcrResponse = null;
            // Add a generic message if no explanation is available
            provider.addMessage(ChatMessage(
              content: "I've processed your request.",
              isUser: false,
            ));
          }
        } else {
          fileUploadOcrResponse = null;
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${convResponse['message']}',
            isUser: false,
          ));
        }
      });

      // Process workflow data outside of setState if available
      if (convResponse['success'] && hasWorkflowData) {
        final data = convResponse['data'];
        final conversationResponse = ConversationResponse.fromJson(data);
        bool hasWorkflowData = false;
        Agent? workflowAgent;

        if (conversationResponse.workflowResult?.agentResults != null) {
          // Check for workflow data
          for (var agent
              in conversationResponse.workflowResult!.agentResults!) {
            if (agent.result?.workFlow?.workFlowDetails != null) {
              hasWorkflowData = true;
            }
            if (agent.agentName == "WORKFLOW") {
              workflowAgent = agent;
            }
          }

          // Process workflow data if available
          if ((workflowAgent != null &&
                  workflowAgent.result?.workFlow != null) ||
              hasWorkflowData) {
            try {
              // Extract workflow data from the API response

              await _updateWorkflowsFromApiResponse(
                  conversationResponse, provider);

              // Check if the widget is still mounted
              if (!mounted) return;

              // Add workflow message using the direct data from API
              setState(() {
                messages.add(ChatMessage(
                  content: '',
                  isUser: false,
                  customContent: _buildWorkflowsResponseDirect(),
                ));
                fileUploadOcrResponse = null;
              });
            } catch (e) {
              // Show a message indicating workflow data couldn't be processed
              setState(() {
                messages.add(ChatMessage(
                  content: 'Workflow data is not available at this time.',
                  isUser: false,
                ));
                fileUploadOcrResponse = null;
              });
            }
          }
        }
      }

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (error) {
      setState(() {
        isLoading = false;
        fileUploadOcrResponse = null;
        provider.addMessage(ChatMessage(
          content: 'Error sending conversation input: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Process API response with translation if needed
  Future<void> _processApiResponseWithTranslation(
      Map<String, dynamic> response, String messageType) async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    if (response['success']) {
      final String answer = response['data']['answer'] ?? 'No answer provided';

      // Create a message with reasoning data
      final ChatMessage message = ChatMessage(
        content: answer,
        isUser: false,
      );

      // Add reasoning data if available
      if (response['reasoning'] != null) {
        message.reasoningData = List<String>.from(response['reasoning']);
      }

      if (fileUploadOcrResponse != null) {
        message.fileData = fileUploadOcrResponse;
      }

      // Check if translation is needed
      if (_multimediaService.needsTranslation(context) &&
          answer != 'No answer provided') {
        // Show loading state for translation
        setState(() {
          isLoading = false;
          provider.addMessage(ChatMessage(
            content: 'Translating response...',
            isUser: false,
          ));
        });

        try {
          // Get the target language
          final targetLanguage =
              _multimediaService.getCurrentLanguageCode(context);
          Logger.info('Translating answer to $targetLanguage');

          // Call the translation service through the multimedia service
          final translationResult = await _multimediaService
              .translateText(answer, targetLanguage, sourceLanguage: 'en');

          if (translationResult['success']) {
            // Get the translated text from the result
            final String translatedText = translationResult['translatedText'];

            // Create a new message with the translated content
            final ChatMessage translatedMessage = ChatMessage(
              content: translatedText,
              isUser: false,
            );

            // Copy reasoning data if available
            if (message.reasoningData != null) {
              translatedMessage.reasoningData = message.reasoningData;
            }

            // Copy file data if available
            if (message.fileData != null) {
              translatedMessage.fileData = message.fileData;
            }

            // Update UI with translated message
            setState(() {
              isLoading = false;

              // Remove the "Translating..." message
              if (messages.isNotEmpty &&
                  messages.last.content.startsWith('Translating response')) {
                provider.messages.removeLast();
              }
              fileUploadOcrResponse = null;
              // Add only the translated message (no original text)
              provider.addMessage(translatedMessage);
            });
          } else {
            // Translation failed, use the original message
            Logger.error('Translation failed: ${translationResult['message']}');
            setState(() {
              isLoading = false;

              // Remove the "Translating..." message
              if (messages.isNotEmpty &&
                  messages.last.content.startsWith('Translating response')) {
                provider.messages.removeLast();
              }
              fileUploadOcrResponse = null;
              // Add the original message
              provider.addMessage(message);
            });
          }
        } catch (e) {
          // Error during translation, use the original message
          Logger.error('Error during translation: $e');
          setState(() {
            isLoading = false;

            // Remove the "Translating..." message
            if (messages.isNotEmpty &&
                messages.last.content.startsWith('Translating response')) {
              provider.messages.removeLast();
            }
            fileUploadOcrResponse = null;
            // Add the original message
            provider.addMessage(message);
          });
        }
      } else {
        // No translation needed, just add the message
        setState(() {
          isLoading = false;
          fileUploadOcrResponse = null; // Clear file upload state
          provider.addMessage(message);
        });
      }
    } else {
      // Add error message
      setState(() {
        isLoading = false;
        fileUploadOcrResponse = null; // Clear file upload state
        provider.addMessage(ChatMessage(
          content: 'Error: ${response['message']}',
          isUser: false,
        ));
      });
    }
  }

  // Method to toggle OCR panel visibility
  void toggleOcrPanel({fileName, extractedText}) {
    setState(() {
      if (messages.isNotEmpty) {
        messages.where(
          (element) =>
              element.fileData != null ? isOcrExist = true : isOcrExist = false,
        );
      }
      if (selectedRole != null ||
          selectedEntity != null ||
          selectedWorkflow != null) {
        showSidePanel = !showSidePanel;
      } else if (isOcrExist) {
        if (fileName != null && extractedText != null) {
          ocrText = fileName;
          ocrFileName = extractedText;
        } else {
          showOcrPanel = !showOcrPanel;
        }
      }
    });
  }

  void closeOcrPanel() {
    setState(() {
      showOcrPanel = false;
    });
  }

  // Track which tab is selected (Discovery or Development)
  // Initialize based on AI toggle state: AI enabled = Discovery tab, AI disabled = Development tab
  bool isDiscoverySelected =
      true; // Since _isAIEnabled starts as true, Discovery tab should be selected initially

  Widget solutionDetailsPanel(BuildContext context) {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    return Container(
      decoration: BoxDecoration(
          color: AppColors.white,
          border: Border.all(color: AppColors.greyBorder)),
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
      width: MediaQuery.of(context).size.width < 1366
          ? MediaQuery.of(context).size.width * 0.25
          : MediaQuery.of(context).size.width /
              5.5, // Adjusted width to match the design
      height: MediaQuery.of(context).size.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab headers
          Row(
            children: [
              Expanded(
                  child: GestureDetector(
                onTap: () {
                  setState(() {
                    isDiscoverySelected = true;
                  });
                },
                child: gradientContainer(
                  isSelected: isDiscoverySelected,
                  child: Center(
                    child: Text(
                      "Discovery",
                      style: FontManager.getCustomStyle(
                          color: isDiscoverySelected
                              ? AppColors.white
                              : AppColors.black,
                          fontWeight: isDiscoverySelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: ResponsiveFontSizes.titleSmall(context)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              )),
              Expanded(
                child: GestureDetector(
                    onTap: () {
                      setState(() {
                        isDiscoverySelected = false;
                      });
                    },
                    child: gradientContainer(
                      isSelected: !isDiscoverySelected,
                      child: Center(
                        child: Text(
                          "Development",
                          style: FontManager.getCustomStyle(
                              color: !isDiscoverySelected
                                  ? AppColors.white
                                  : AppColors.black,
                              fontWeight: !isDiscoverySelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize:
                                  ResponsiveFontSizes.titleSmall(context)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    )),
              ),
            ],
          ),

          // Content area
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Conditional rendering based on selected tab
                  isDiscoverySelected
                      ? _buildDiscoveryWidget(provider)
                      : _buildDevelopmentWidget(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ExpansionTileItem expansionTileItem({title, List? list}) {
    return ExpansionTileItem(
        tilePadding: EdgeInsets.all(AppSpacing.xs),
        childrenPadding: EdgeInsets.all(AppSpacing.xs),
        isDefaultVerticalPadding: false,
        initiallyExpanded: false,
        isHasTopBorder: true,
        isHasBottomBorder: true,
        isHasLeftBorder: true,
        isHasRightBorder: true,
        isHasTrailing: false,
        border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        title: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: FontManager.getCustomStyle(
                    color: AppColors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveFontSizes.titleSmall(context)),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              "0%",
              style: FontManager.getCustomStyle(
                  color: Colors.black,
                  fontSize: ResponsiveFontSizes.titleSmall(context)),
            ),
          ],
        ),
        children: list != null
            ? list
                .map(
                  (e) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        (e["isOptional"] == true)
                            ? Text(
                                "Core Optional",
                                style: FontManager.getCustomStyle(
                                  color: AppColors.black,
                                  fontSize:
                                      ResponsiveFontSizes.titleSmall(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              )
                            : Text(
                                "Mandatory",
                                style: FontManager.getCustomStyle(
                                  color: AppColors.black,
                                  fontSize:
                                      ResponsiveFontSizes.titleSmall(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                (e["value"] ?? '').toString(),
                                style: FontManager.getCustomStyle(
                                  color: AppColors.black,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontWeight: FontWeight.w600,
                                  fontSize:
                                      ResponsiveFontSizes.titleMedium(context),
                                ),
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                )
                .toList()
            : []);
  }

  Widget gradientContainer({child, isSelected = false}) {
    return Container(
        decoration: isSelected
            ? BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppSpacing.xs),
                    topRight: Radius.circular(AppSpacing.xs)),
                gradient: LinearGradient(
                    colors: [AppColors.primaryBlue, AppColors.darkBlue],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight))
            : BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppSpacing.xs),
                    topRight: Radius.circular(AppSpacing.xs)),
                color: AppColors.grey,
              ),
        padding: EdgeInsets.all(AppSpacing.xs),
        child: child);
  }

  Widget textStatusContainer(text, value) {
    return Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(AppSpacing.xxs),
            bottomRight: Radius.circular(AppSpacing.xxs),
            topLeft: Radius.circular(0),
            topRight: Radius.circular(0),
          ),
          color: Color(0xffE9F2F7),
        ),
        padding: EdgeInsets.only(top: 20, right: 12, bottom: 20, left: 10),
        child: Row(
          children: [
            Expanded(
              child: Text(
                text,
                style: FontManager.getCustomStyle(
                    color: AppColors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: ResponsiveFontSizes.titleSmall(context)),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              value,
              style: FontManager.getCustomStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: ResponsiveFontSizes.titleSmall(context)),
            ),
          ],
        ));
  }

  // Build Discovery widget with selected code
  Widget _buildDiscoveryWidget(WebHomeProviderStatic provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Total Discovery Progress section
        textStatusContainer("Total Discovery Progress", "0%"),

        SizedBox(height: AppSpacing.sm),

        // Business Requirement Document header
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSpacing.xs),
                topRight: Radius.circular(AppSpacing.xs)),
            color: AppColors.lightBlueColor,
          ),
          padding: EdgeInsets.all(AppSpacing.xs),
          child: Text("Business Requirement Document",
              style: FontManager.getCustomStyle(
                  color: AppColors.black,
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontFamily: FontManager.fontFamilyTiemposText)),
        ),
        SizedBox(height: AppSpacing.xs),
        // Expansion tiles for different sections
        ExpansionTileGroup(
          toggleType: ToggleType.expandOnlyCurrent,
          spaceBetweenItem: AppSpacing.xxs,
          children: [
            expansionTileItem(title: "Organizational Structure & Role", list: [
              {
                "value": "Business Agent Identification",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Business Agent Definitions",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Agent Collaboration Framework",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Agent Enhancement Opportunities",
                "isOptional": true,
                "percentage": "0 %"
              }
            ]),
            expansionTileItem(title: "Data & Entity Management", list: [
              {
                "value": "Agent Information Requirements",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Entity Structure for Agent Workflows",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Information Quality Framework",
                "isOptional": false,
                "percentage": "0 %"
              },
            ]),
            expansionTileItem(title: "Business Processes & Workflows", list: [
              {
                "value": "Core Agent Workflows",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Agent Decision Support",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Agent Efficiency Enhancement",
                "isOptional": false,
                "percentage": "0 %"
              },
            ]),
            expansionTileItem(title: "Functions & Operations", list: [
              {
                "value": "Core Agent Functions",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Agent Interface Requirements",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Agent Intelligence Support",
                "isOptional": false,
                "percentage": "0 %"
              },
            ]),
            expansionTileItem(title: "User Interface & Experience", list: [
              {
                "value": "Business Policy Rules",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Data Validation Rules",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Security & Access Policies",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Workflow Rules",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Calculation Rules",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Notification Rules",
                "isOptional": true,
                "percentage": "0 %"
              }
            ]),
            expansionTileItem(title: "Intelligence & Analytics", list: [
              {
                "value": "Metrics & Monitoring for Agents",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Function Value & Economics",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Security & Compliance Framework",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Integration & Connectivity",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Collaboration & Communication",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "AI-Powered Agent Enhancement",
                "isOptional": true,
                "percentage": "0 %"
              },
            ]),
            expansionTileItem(title: "Performance & Metrics", list: [
              {
                "value": "Cost Structure Definition",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Value Generation Framework",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Financial Performance Tracking",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Master Data Requirements",
                "isOptional": true,
                "percentage": "0 %"
              }
            ]),
            expansionTileItem(title: "Financial Management", list: [
              {
                "value": "Business Domain Specialization",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Solution Customization Requirements",
                "isOptional": true,
                "percentage": "0 %"
              },
              {
                "value": "Platform Configuration",
                "isOptional": false,
                "percentage": "0 %"
              }
            ]),
            expansionTileItem(
                title: "Security, Compliance & Governance",
                list: [
                  {
                    "value": "Discovery Quality Assessment",
                    "isOptional": false,
                    "percentage": "0 %"
                  },
                  {
                    "value": "Critical Assumptions & Unknowns",
                    "isOptional": true,
                    "percentage": "0 %"
                  },
                  {
                    "value": "Agent-Centric Solution Readiness",
                    "isOptional": false,
                    "percentage": "0 %"
                  }
                ]),
            expansionTileItem(title: "Integration & Connectivity", list: [
              {
                "value": "Technical Artifact Generation Requirements",
                "isOptional": false,
                "percentage": "0 %"
              },
              {
                "value": "Agent Success Validation Framework",
                "isOptional": true,
                "percentage": "0 %"
              }
            ]),
          ],
        ),
      ],
    );
  }

  // Build Development widget
  Widget _buildDevelopmentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Single main container with Business Terms Identified header and progress items
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xffB4B4B4), width: 0.5),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(0),
              topRight: Radius.circular(0),
              bottomLeft: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
            color: Color(0xffE9F2F7),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Business Terms Identified header (integrated as part of main container)
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Business Terms Identified",
                        style: FontManager.getCustomStyle(
                            color: AppColors.black,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: ResponsiveFontSizes.titleSmall(context)),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(
                      width: AppSpacing.sm,
                    ),
                    Text(
                      "78%",
                      style: FontManager.getCustomStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          fontSize: ResponsiveFontSizes.titleSmall(context)),
                    ),
                  ],
                ),
              ),

              // Progress items inside the same container
              Padding(
                padding: EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    _buildDevelopmentProgressItem(
                      title: "Roles",
                      count: "4 defined",
                      icon: Icons.people_outline,
                      iconColor: Color(0xff0058FF),
                      progress: 1.0, // Full progress to match design
                    ),
                    _buildDevelopmentProgressItem(
                      title: "Objects",
                      count: "23 Created",
                      icon: Icons.view_in_ar_outlined,
                      iconColor: Color(0xff0058FF),
                      progress: 1.0, // Full progress to match design
                    ),
                    _buildDevelopmentProgressItem(
                      title: "Objects (GO)",
                      count: "13 Created",
                      icon: Icons.crop_square_outlined,
                      iconColor: Color(0xff0058FF),
                      progress: 1.0, // Full progress to match design
                    ),
                    _buildDevelopmentProgressItem(
                      title: "Functions (LO)",
                      count: "36 Created",
                      icon: Icons.hub_outlined,
                      iconColor: Color(0xff0058FF),
                      progress: 1.0, // Full progress to match design
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppSpacing.md),

        // Bottom section - Management categories
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xffB4B4B4), width: 0.5),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs, vertical: AppSpacing.xs),
                  decoration: BoxDecoration(
                    color: Color(0xffCADBE6),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Text(
                    "Business Terms Identified",
                    style: FontManager.getCustomStyle(
                        color: AppColors.black,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: ResponsiveFontSizes.titleSmall(context)),
                  ),
                ),
              ),

              // Management categories
              Padding(
                padding: EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    _buildManagementCategory(
                      title: "HR Management",
                      description:
                          "Employee onboarding and exit processes, performance reviews, and talent management",
                      pending: "Pending",
                    ),
                    _buildManagementCategory(
                        title: "Employee Lifecycle",
                        description:
                            "Complete employee journey from recruitment to retirement",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Performance Management",
                        description:
                            "Performance evaluation and goal tracking systems",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Leave Management",
                        description:
                            "Time-off requests, approvals, and leave balance tracking",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Recruitment Process",
                        description:
                            "Candidate sourcing, interviewing, and hiring workflows",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Organizational Chart",
                        description:
                            "Company structure and reporting relationships",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Role Permission",
                        description:
                            "Company structure and reporting relationships",
                        pending: "Pending"),
                    _buildManagementCategory(
                        title: "Organizational Chart",
                        description:
                            "Company structure and reporting relationships",
                        isLast: true,
                        pending: "Pending"),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppSpacing.md),

        // New section - Security & Compliance Framework
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xffB4B4B4), width: 0.5),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs, vertical: AppSpacing.xs),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [AppColors.primaryBlue, AppColors.darkBlue],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Text(
                  "LO Stack Assignments",
                  style: FontManager.getCustomStyle(
                      color: AppColors.white,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleSmall(context)),
                ),
              ),

              // Security and compliance components
              Padding(
                padding: EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                    ),
                    _buildStackAssignment(
                      title: "Input Stack",
                      description: "Universal",
                      isLast: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to build development progress items with progress bars
  Widget _buildDevelopmentProgressItem({
    required String title,
    required String count,
    required IconData icon,
    required Color iconColor,
    required double progress,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Colors.white,
      ),
      child: Row(
        children: [
          // Icon on the left
          Container(
            margin: EdgeInsets.only(right: AppSpacing.xs),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          // Title, count and progress bar in one column
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and count row
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: FontManager.getCustomStyle(
                          color: AppColors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontWeight: FontWeight.w600,
                          fontSize: ResponsiveFontSizes.titleSmall(context),
                        ),
                      ),
                    ),
                    Text(
                      count,
                      style: FontManager.getCustomStyle(
                        color: AppColors.black,
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppSpacing.xs),
                // Progress bar
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Color(0xff4CAF50), // Green color for progress
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build simple development items
  Widget _buildSimpleDevelopmentItem(String title, String count) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                color: AppColors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
                fontWeight: FontWeight.w600,
                fontSize: ResponsiveFontSizes.titleSmall(context),
              ),
            ),
          ),
          Text(
            count,
            style: FontManager.getCustomStyle(
              color: AppColors.black,
              fontWeight: FontWeight.w600,
              fontSize: ResponsiveFontSizes.titleSmall(context),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the Business Terms Identified section
  Widget _buildBusinessTermsSection() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              "Business Terms Identified",
              style: FontManager.getCustomStyle(
                color: AppColors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
                fontWeight: FontWeight.w600,
                fontSize: ResponsiveFontSizes.titleSmall(context),
              ),
            ),
          ),
          Text(
            "78%",
            style: FontManager.getCustomStyle(
              color: AppColors.black,
              fontWeight: FontWeight.w600,
              fontSize: ResponsiveFontSizes.titleSmall(context),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build individual development items
  Widget _buildDevelopmentItem({
    required String title,
    required String description,
    required IconData icon,
    Color? iconColor,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with icon
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(right: AppSpacing.xs),
                child: Icon(
                  icon,
                  color: Color(0xff0058FF),
                  size: 20,
                ),
              ),
              Expanded(
                child: Text(
                  title,
                  style: FontManager.getCustomStyle(
                    color: AppColors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.xs),
          // Description
          Padding(
            padding: EdgeInsets.only(left: 32), // Align with title text
            child: Text(
              description,
              style: FontManager.getCustomStyle(
                color: AppColors.textGreyColor,
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 12,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build management category items
  Widget _buildManagementCategory({
    required String title,
    required String description,
    required String pending,
    bool isLast = false,
  }) {
    return Container(
      // color: Color(0xffFFFFFF),
      margin: EdgeInsets.only(bottom: isLast ? 0 : AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.xs),
      decoration: BoxDecoration(
        // border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Color(0xffF1F3F4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with icon
          Row(
            children: [
              SizedBox(width: 18), // Replace Padding with SizedBox for spacing
              Expanded(
                child: Text(
                  title,
                  style: FontManager.getCustomStyle(
                    color: AppColors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.xxs),
          // Description
          Padding(
            padding: EdgeInsets.only(left: 18), // Align with title text
            child: Text(
              description,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: FontManager.getCustomStyle(
                color: AppColors.textGreyColor,
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 9,
              ),
            ),
          ),

          SizedBox(height: AppSpacing.xxs),
          // Description
          Padding(
            padding: EdgeInsets.only(left: 18), // Align with title text
            child: Text(
              pending,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: FontManager.getCustomStyle(
                color: Color(0xff6F747D),
                fontFamily: FontManager.fontFamilyTiemposText,
                fontWeight: FontWeight.w600,
                fontSize: 9,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build management category items
  Widget _buildStackAssignment({
    required String title,
    required String description,
    bool isLast = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.xs),
      decoration: BoxDecoration(
        // border: Border.all(color: AppColors.darkGreyBorder, width: 0.5),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        color: Color(0xffF1F3F4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with icon
          Row(
            children: [
              SizedBox(width: 18), // Replace Padding with SizedBox for spacing
              Expanded(
                child: Text(
                  title,
                  style: FontManager.getCustomStyle(
                    color: AppColors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.xxs),
          // Description
          Padding(
            padding: EdgeInsets.only(left: 18), // Align with title text
            child: Text(
              description,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: FontManager.getCustomStyle(
                color: AppColors.textGreyColor,
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 9,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Extract agent data from API response
  AgentData _extractAgentDataFromResponse(Agent agent, List<String?> versions) {
    try {
      Logger.info(
          '🔍 DATA SOURCE CHECK: Extracting agent data from API response');

      Logger.info(
          '🔍 DATA SOURCE CHECK: Processing agent with name: ${agent.agentName}');

      // Create a new AgentData object
      AgentData agentData = AgentData();
      List<AgentInfo> agentInfoList = [];

      // Check if result is available
      if (agent.result == null) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: Agent result is null, returning empty AgentData');

        return agentData;
      }

      // Log all available fields in the result for debugging
      final resultJson = agent.result!.toJson();
      Logger.info(
          '🔍 DATA SOURCE CHECK: Agent result fields: ${resultJson.keys.join(', ')}');

      // Process standard agents field if available
      if (agent.result?.agents != null && agent.result!.agents!.isNotEmpty) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: Processing standard agents field with ${agent.result!.agents!.length} agents');

        for (var agentElement in agent.result!.agents!) {
          // Create agent info from agent element
          AgentInfo agentInfo = _createAgentInfoFromElement(agentElement);
          agentInfoList.add(agentInfo);
          Logger.info(
              '🔍 DATA SOURCE CHECK: Added agent info from standard agents field: ${agentElement.title}');
        }
      }
      // Check for alternative fields that might contain agent-related data
      else {
        Logger.info(
            '🔍 No standard agents field found, checking for alternative data');

        // Try to extract agent data from other fields
        final fieldsToCheck = [
          'business_logic',
          'core_components',
          'data_flow',
          'decision_points',
          'error_handling',
          'interfaces'
        ];

        List<AgentSection> sections = [];

        for (var field in fieldsToCheck) {
          if (resultJson.containsKey(field) && resultJson[field] is List) {
            final items = List<String>.from(resultJson[field]);
            if (items.isNotEmpty) {
              Logger.info('🔍 Found ${items.length} items in $field field');

              // Create a section for this field
              sections.add(AgentSection(
                id: 'section_${field}_${DateTime.now().millisecondsSinceEpoch}',
                title: field.replaceAll('_', ' ').toUpperCase(),
                abbreviation: field.substring(0, 2).toUpperCase(),
                items: items,
              ));
            }
          }
        }

        if (sections.isNotEmpty) {
          Logger.info(
              '🔍 Created ${sections.length} sections from alternative fields');

          // Create a synthetic agent element
          final syntheticElement = AgentElement(
            id: 'agent_${DateTime.now().millisecondsSinceEpoch}',
            title: '${agent.agentName} Information',
            description: 'Information extracted from ${agent.agentName} agent',
            version: '1.0',
            sections: sections,
          );

          // Create agent info from synthetic element
          AgentInfo agentInfo = AgentInfo(
            id: syntheticElement.id ??
                'agent_${DateTime.now().millisecondsSinceEpoch}',
            title: syntheticElement.title ?? 'Untitled Agent',
            description: syntheticElement.description ?? '',
            version: syntheticElement.version ?? '1.0',
            sections: _processAgentSections(syntheticElement.sections),
          );

          agentInfoList.add(agentInfo);
        }
      }

      // Create system info
      AgentSystemInfo systemInfo = AgentSystemInfo(
          agentCount: agentInfoList.length,
          bulletPoints: [
            "Agent data extracted from API response",
            "Contains ${agentInfoList.length} agents",
            "Processed from ${agent.agentName} agent"
          ],
          headerText: "Agent Information from ${agent.agentName}",
          systemVersions: versions,
          // Initialize selectedVersion with the first version if available
          selectedVersion: versions.isNotEmpty ? versions.first : null);

      // Update the AgentData object
      agentData = AgentData(
        agents: agentInfoList,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 API SUCCESS: Extracted agent data from API response with ${agentInfoList.length} agents');
      return agentData;
    } catch (e) {
      Logger.error('Error extracting agent data from response: $e');
      // Return empty AgentData in case of error
      return AgentData();
    }
  }

  // Helper method to create AgentInfo from AgentElement
  AgentInfo _createAgentInfoFromElement(AgentElement agentElement) {
    return AgentInfo(
      id: agentElement.id ?? 'agent_${DateTime.now().millisecondsSinceEpoch}',
      title: agentElement.title ?? 'Untitled Agent',
      description: agentElement.description ?? '',
      version: agentElement.version ?? '1.0',
      createdBy: agentElement.createdBy ?? '',
      createdDate: agentElement.createdDate != null
          ? _parseDate(agentElement.createdDate!)
          : null,
      modifiedBy: agentElement.modifiedBy ?? '',
      modifiedDate: agentElement.modifiedDate != null
          ? _parseDate(agentElement.modifiedDate!)
          : null,
      sections: _processAgentSections(agentElement.sections),
    );
  }

  // Helper method to process agent sections
  List<AgentInfoSection> _processAgentSections(List<AgentSection>? sections) {
    if (sections == null || sections.isEmpty) {
      return [];
    }

    List<AgentInfoSection> result = [];

    for (var section in sections) {
      // Process tabs if available
      List<AgentInfoTab>? tabs;
      if (section.tabs != null && section.tabs!.isNotEmpty) {
        tabs = section.tabs!
            .map((tab) => AgentInfoTab(
                  name: tab.name,
                  isSelected: tab.isSelected,
                  data: tab.data?.cast<String>(),
                ))
            .toList();
      }

      // Create agent section
      AgentInfoSection agentSection = AgentInfoSection(
        id: section.id ?? 'section_${DateTime.now().millisecondsSinceEpoch}',
        title: section.title ?? 'Untitled Section',
        abbreviation: section.abbreviation ?? '',
        items: section.items?.cast<String>() ?? [],
        tabs: tabs,
      );

      result.add(agentSection);
    }

    return result;
  }

  // Helper method to process question text and format it with bold styling
  String _processQuestionText(String questionText) {
    // Pattern to match "**Question:**" followed by text until "?"
    final RegExp questionPattern =
        RegExp(r'\*\*Question:\*\*(.*?)\?', multiLine: true, dotAll: true);

    String processedText = questionText;

    // Find all matches and replace them
    final matches = questionPattern.allMatches(questionText);
    for (final match in matches) {
      final fullMatch = match.group(0) ?? '';
      final extractedContent = match.group(1) ?? '';

      // Remove "**Question:**" and keep just the content
      final questionContent = extractedContent.trim();
      processedText =
          processedText.replaceFirst(fullMatch, '${questionContent}?');
    }

    return processedText;
  }

  // Helper method to build formatted question widget with bold text
  Widget _buildFormattedQuestionWidget(String questionText) {
    // Pattern to match the question part that should be bold
    final RegExp questionPattern =
        RegExp(r'(.*?)\?', multiLine: true, dotAll: true);

    final match = questionPattern.firstMatch(questionText);
    if (match != null) {
      final questionContent = match.group(1) ?? '';

      return RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
          children: [
            TextSpan(
              text: '${questionContent}?',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ],
        ),
      );
    }

    // Fallback to regular text if no pattern matches
    return Text(
      questionText,
      style: TextStyle(
        fontSize: 14,
        fontFamily: 'TiemposText',
        color: Colors.black,
      ),
    );
  }

  // Helper method to parse dates in different formats
  DateTime _parseDate(dynamic dateInput) {
    // Default date to return if parsing fails
    final defaultDate = DateTime(2000, 1, 1);

    try {
      // If null, return default date
      if (dateInput == null) {
        return defaultDate;
      }

      // If already a DateTime, return it
      if (dateInput is DateTime) {
        return dateInput;
      }

      // If it's a String, try to parse it
      if (dateInput is String) {
        try {
          // Try standard ISO format first
          return DateTime.parse(dateInput);
        } catch (e) {
          try {
            // Try DD/MM/YYYY format
            final parts = dateInput.split('/');
            if (parts.length == 3) {
              final day = int.parse(parts[0]);
              final month = int.parse(parts[1]);
              final year = int.parse(parts[2]);
              return DateTime(year, month, day);
            }
          } catch (e) {
            // Log the error but don't throw
            Logger.error('Error parsing date "$dateInput": $e');
          }

          try {
            // Try MM/DD/YYYY format
            final parts = dateInput.split('/');
            if (parts.length == 3) {
              final month = int.parse(parts[0]);
              final day = int.parse(parts[1]);
              final year = int.parse(parts[2]);
              return DateTime(year, month, day);
            }
          } catch (e) {
            // Log the error but don't throw
            Logger.error('Error parsing date "$dateInput" as MM/DD/YYYY: $e');
          }
        }
      }

      // If we get here, all parsing attempts failed
      return defaultDate;
    } catch (e) {
      // Catch any unexpected errors
      Logger.error('Unexpected error parsing date: $e');
      return defaultDate;
    }
  }

  Widget hoverButtons({icon, onPressed}) {
    return HoverButton(
      icon: icon,
      onPressed: onPressed,
    );
  }

  // Create a response with roles and use cases table
  Widget _buildRolesResponse() {
    // Show loading indicator while roles are being loaded
    if (isLoadingRoles ||
        (globalAgentData.agents == null || globalAgentData.agents!.isEmpty)) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading roles data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with icon
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset('assets/images/icons/box.svg',
                // fit: BoxFit.fill,
                height: 24,
                width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.xs),

        //Table with roles
        Container(
          padding: EdgeInsets.all(
            AppSpacing.xxs,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Color(0xFFBEBEBE), width: 0.5),
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
          ),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFBEBEBE), width: 0.5),
              // borderRadius: BorderRadius.circular(AppSpacing.sm),
              // color: Colors.white,
            ),
            child: Column(
              children: [
                // Header row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppSpacing.xxs),
                      topRight: Radius.circular(AppSpacing.xxs),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      //  Text(globalAgentData.systemInfo?.headerText ?? '',
                      // style: TextStyle(
                      //   fontFamily: 'TiemposText',
                      //   fontSize: 14,
                      //   fontWeight: FontWeight.w500,
                      // )),
                      // Expanded(
                      //   child: Text(
                      //     'Roles And Usecases',
                      //     style: TextStyle(
                      //       fontWeight: FontWeight.w500,
                      //       fontSize: 12,
                      //       fontFamily: 'TiemposText',
                      //     ),
                      //   ),
                      // ),
                      // Custom dropdown that looks exactly like the image
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          globalAgentData.systemInfo?.systemVersions != null &&
                                  (globalAgentData.systemInfo?.systemVersions)!
                                      .isNotEmpty
                              ? CustomEntityVersionDropdown(
                                  versions: globalAgentData
                                      .systemInfo!.systemVersions!
                                      .map(
                                        (e) => e.toString(),
                                      )
                                      .toList(),
                                  selectedVersion: globalAgentData
                                          .systemInfo?.selectedVersion ??
                                      '',
                                  onVersionSelected: (String version) {
                                    setState(() {
                                      globalAgentData.systemInfo
                                          ?.selectedVersion = version;
                                    });
                                  },
                                )
                              : Container(),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber.shade100,
                              borderRadius:
                                  BorderRadius.circular(AppSpacing.xxs),
                            ),
                            child: Text(
                              '${globalAgentData.systemInfo?.agentCount ?? 0} Agent',
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'TiemposText',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Role rows - using Column instead of ListView.builder to avoid syntax issues
                Column(
                  children: globalAgentData.agents != null &&
                          globalAgentData.agents!.isNotEmpty
                      ? globalAgentData.agents!.map((agent) {
                          // Convert AgentInfo to RoleInfo
                          final role = RoleInfo(
                            id: agent.id,
                            title: agent.title,
                            description: agent.description,
                            version: agent.version,
                            createdBy: agent.createdBy,
                            createdDate: _formatDate(
                                agent.createdDate ?? DateTime.now()),
                            modifiedBy: agent.modifiedBy,
                            modifiedDate: _formatDate(
                                agent.modifiedDate ?? DateTime.now()),
                            // Extract use cases from agent sections
                            useCases: agent.sections
                                .where((section) => section.title
                                    .toLowerCase()
                                    .contains('use case'))
                                .expand((section) {
                              List<String> temp = [];
                              for (var item in section.items) {
                                temp.add(item.toString());
                              }
                              return temp;
                            }).toList(),
                            // Extract permissions from agent sections
                            permissions: {
                              'entities': agent.sections
                                  .where((section) => section.title
                                      .toLowerCase()
                                      .contains('entity'))
                                  .expand((section) {
                                List<String> temp = [];
                                for (var item in section.items) {
                                  temp.add(item.toString());
                                }
                                return temp;
                              }).toList(),
                              'objectives': agent.sections
                                  .where((section) => section.title
                                      .toLowerCase()
                                      .contains('objective'))
                                  .expand((section) {
                                List<String> temp = [];
                                for (var item in section.items) {
                                  temp.add(item.toString());
                                }
                                return temp;
                              }).toList(),
                            },
                          );

                          // Explicitly check if this role is selected
                          final bool isSelected = selectedRole != null &&
                              selectedRole!.id == role.id;

                          return BuildRoleCard(
                            role: role,
                            isSelected: isSelected,
                            onRoleTap: (selectedRole) {
                              setState(() {
                                this.selectedRole = selectedRole;
                                selectedEntity = null;
                                selectedWorkflow = null;
                                showSidePanel = true;
                              });
                            },
                          );
                        }).toList()
                      : [], // No fallback, just return an empty list if globalAgentData is empty
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: AppSpacing.xs),
      ],
    );
  }

  // Build side panel for role details
  Widget _buildRoleDetailsPanel(RoleInfo role) {
    final ScrollController roleDetailScrollController = ScrollController();
    final Map<String, GlobalKey> roleSectionKeys = {
      'useCases': GlobalKey(),
      'permissions': GlobalKey(),
    };
    void scrollToRoleSection(String sectionId) {
      if (roleSectionKeys.containsKey(sectionId)) {
        final RenderObject? renderObject =
            roleSectionKeys[sectionId]?.currentContext?.findRenderObject();
        if (renderObject != null) {
          // RenderBox renderBox = renderObject as RenderBox;
          // _roleDetailScrollController.animateTo(renderBox.localToGlobal(Offset.zero).dy,  duration: const Duration(milliseconds: 300),
          //   curve: Curves.easeInOut,);
          roleDetailScrollController.position.ensureVisible(
            renderObject,
            alignment: 0.0,
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeInOut,
          );
        }
      }
    }

    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(color: Colors.grey.shade300, width: 1),
          ),
          boxShadow: [
            BoxShadow(
              color:
                  Color(0xff9B9B9B).withValues(alpha: 0.14), // 0.14 * 255 = ~36
              blurRadius: 20,
              offset: Offset(-3, 0),
            ),
          ],
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // Header with role title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30),
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        //   const Color.fromARGB(255, 92, 163, 234),
                        radius: 10,
                        child: Icon(
                          Icons.person_outline,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xxs),
                      Expanded(
                        child: Text(
                          role.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText",
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                OcrPanelToggleButton(
                  isShown: showOcrPanel || showSidePanel,
                  onToggle: toggleOcrPanel,
                ),
              ],
            ),
          ),

          // Content area with UC and PR labels
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column for UC and PR labels
                Container(
                  width: 30,
                  padding: EdgeInsets.only(top: 3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // UC label aligned with Use cases section
                      Padding(
                        padding: EdgeInsets.only(left: 8, top: 16),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => scrollToRoleSection('useCases'),
                            child: Text(
                              'UC',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align PR with Permissions section
                      SizedBox(
                          height:
                              10), // Adjust this height based on your content
                      // PR label aligned with Permissions section
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => scrollToRoleSection('permissions'),
                            child: Text(
                              'PR',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Right column for content - keep your existing content structure
                Expanded(
                  child: SingleChildScrollView(
                    controller: roleDetailScrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Use cases section - keep your existing implementation
                        Container(
                          key: roleSectionKeys['useCases'],
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                'Use cases',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  fontFamily: "TiemposText",
                                ),
                              ),
                              SizedBox(height: 8),
                              // Get use cases from globalAgentData if available
                              Builder(
                                builder: (context) {
                                  // Get use cases directly from the role
                                  List<String> useCases = [];

                                  if (role.useCases != null) {
                                    useCases = role.useCases!;
                                  }

                                  if (useCases.isNotEmpty) {
                                    return RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontFamily: "TiemposText",
                                          color: Colors.black,
                                        ),
                                        children: [
                                          // Role title with tooltip - keep your existing tooltip
                                          WidgetSpan(
                                            child: MouseRegion(
                                              // cursor: SystemMouseCursors.click,
                                              child: Tooltip(
                                                richMessage: WidgetSpan(
                                                  child: UserProfileCard(
                                                    id: role.id ?? 'so008',
                                                    version: role.version ??
                                                        'V00019',
                                                    displayName: role.title,
                                                    createdBy: role.createdBy ??
                                                        'John Smith',
                                                    createdDate:
                                                        role.createdDate ??
                                                            '10/04/2025',
                                                    modifiedBy:
                                                        role.modifiedBy ??
                                                            'Jane Doe',
                                                    modifiedDate:
                                                        role.modifiedDate ??
                                                            '28/04/2025',
                                                    roleTitle: role.title,
                                                    roleDescription:
                                                        role.description,
                                                    width:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width /
                                                            3,
                                                  ),
                                                ),
                                                preferBelow: true,
                                                verticalOffset: 20,
                                                padding: EdgeInsets.zero,
                                                margin: EdgeInsets.zero,
                                                showDuration:
                                                    Duration(seconds: 10),
                                                decoration: BoxDecoration(
                                                  color: Colors.transparent,
                                                  boxShadow: [],
                                                ),
                                                child: Text(
                                                  "${role.title} ",
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.bold,
                                                    fontFamily: 'TiemposText',
                                                    color: Colors.black,
                                                    // decoration: TextDecoration.underline,
                                                    decorationColor:
                                                        Colors.blue.shade700,
                                                    decorationThickness: 1,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),

                                          // All use cases as a single text without line break
                                          // Replace the single TextSpan with individual clickable spans
                                          // Replace the TextSpan with a GestureDetector wrapped TextSpan

// Replace the single TextSpan with individual clickable spans
                                          ...role.useCases!
                                              .map((useCase) => WidgetSpan(
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        // final RenderBox renderBox =
                                                        //     context.findRenderObject()
                                                        //         as RenderBox;
                                                        // final position =
                                                        //     renderBox.localToGlobal(
                                                        //         Offset.zero);
                                                        // Show tooltip with chat field when clicked

                                                        showDialog(
                                                          context: context,
                                                          barrierColor: Colors
                                                              .transparent,
                                                          builder: (BuildContext
                                                              context) {
                                                            return Stack(
                                                              children: [
                                                                Positioned(
                                                                  right: 30,
                                                                  // position.dx,
                                                                  top: 130,
                                                                  //position.dy ,
                                                                  // + 20,
                                                                  child: Dialog(
                                                                    backgroundColor:
                                                                        Colors
                                                                            .white,
                                                                    elevation:
                                                                        8,
                                                                    shape:
                                                                        RoundedRectangleBorder(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              12),
                                                                    ),
                                                                    insetPadding:
                                                                        EdgeInsets
                                                                            .zero,

                                                                    // bottom: 50,
                                                                    // ),
                                                                    child:
                                                                        Container(
                                                                      margin: EdgeInsets.symmetric(
                                                                          // horizontal: AppSpacing.md,
                                                                          // vertical: AppSpacing.md,
                                                                          ),
                                                                      decoration:
                                                                          BoxDecoration(
                                                                        color: Colors
                                                                            .white,
                                                                        borderRadius:
                                                                            BorderRadius.circular(AppSpacing.md),
                                                                        border: Border.all(
                                                                            color: Color.fromARGB(
                                                                                255,
                                                                                94,
                                                                                162,
                                                                                192),
                                                                            width:
                                                                                1),
                                                                        boxShadow: [],
                                                                      ),
                                                                      constraints:
                                                                          BoxConstraints(
                                                                        maxHeight:
                                                                            300,
                                                                        minHeight:
                                                                            200,
                                                                      ),
                                                                      width: MediaQuery.of(context)
                                                                              .size
                                                                              .width /
                                                                          3.8,
                                                                      padding: EdgeInsets.all(
                                                                          AppSpacing
                                                                              .xs),
                                                                      child:
                                                                          Column(
                                                                        mainAxisSize:
                                                                            MainAxisSize.min,
                                                                        children: [
                                                                          Flexible(
                                                                            child:
                                                                                TextField(
                                                                              controller: chatController,
                                                                              maxLines: null,
                                                                              decoration: InputDecoration(
                                                                                focusColor: Colors.transparent,
                                                                                hintText: "You want to make a comment on ${role.title} use cases? Please put your points in details I will help you to integrate it.",
                                                                                hintStyle: TextStyle(
                                                                                  fontSize: 14,
                                                                                  fontWeight: FontWeight.normal,
                                                                                  color: Colors.grey,
                                                                                  fontFamily: "TiemposText",
                                                                                ),
                                                                                hoverColor: Colors.transparent,
                                                                                border: OutlineInputBorder(borderSide: BorderSide.none),
                                                                                enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                                                                focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                                                                errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                                                                disabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                                                              ),
                                                                              onSubmitted: (_) => _sendMessage(),
                                                                            ),
                                                                          ),
                                                                          SizedBox(
                                                                              height: 40),
                                                                          Row(
                                                                            mainAxisAlignment:
                                                                                MainAxisAlignment.end,
                                                                            children: [
                                                                              hoverButtons(icon: Icon(Icons.mic_rounded), onPressed: () {}),
                                                                              hoverButtons(icon: Icon(Icons.attachment), onPressed: () {}),
                                                                              hoverButtons(icon: Icon(Icons.add), onPressed: () {}),
                                                                              hoverButtons(
                                                                                icon: Icon(Icons.arrow_upward),
                                                                                onPressed: _sendMessage,
                                                                              ),
                                                                            ],
                                                                          )
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child: Text(
                                                        "$useCase, ",
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          fontFamily:
                                                              "TiemposText",
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                    ),
                                                  )),
                                        ],
                                      ),
                                    );
                                  }
                                  return Text(
                                    'No use cases defined for this role.',
                                    style: TextStyle(
                                      fontFamily: 'TiemposText',
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      color: Colors.grey.shade700,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),

                        // Permissions section - keep your existing implementation
                        Container(
                          key: roleSectionKeys['permissions'],
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Permissions',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  fontFamily: "TiemposText",
                                ),
                              ),
                              SizedBox(height: 8),
                              RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: role.title,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight
                                            .bold, // Bold for role title
                                        color: Colors.black,
                                        fontFamily: "TiemposText",
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' has the following permissions:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight
                                            .normal, // Normal for the rest of text
                                        color: Colors.black,
                                        fontFamily: "TiemposText",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // MouseRegion(
                              //   // cursor: SystemMouseCursors.click,
                              //   child: Tooltip(
                              //     richMessage: WidgetSpan(
                              //       child: Container(
                              //         margin:
                              //             EdgeInsets.only(left: 10, top: 10),
                              //         width: 350,
                              //         decoration: BoxDecoration(
                              //           color: Colors.white,
                              //           borderRadius: BorderRadius.circular(8),
                              //           boxShadow: [
                              //             BoxShadow(
                              //               color: Colors.black.withValues(
                              //                   alpha: 51), // 0.2 * 255 = ~51
                              //               blurRadius: 10,
                              //               offset: Offset(0, 3),
                              //             ),
                              //           ],
                              //         ),
                              //         child: Column(
                              //           mainAxisSize: MainAxisSize.min,
                              //           crossAxisAlignment:
                              //               CrossAxisAlignment.start,
                              //           children: [
                              //             // Blue header with icon
                              //             Container(
                              //               padding: EdgeInsets.all(12),
                              //               decoration: BoxDecoration(
                              //                 color: Colors.blue.shade700,
                              //                 borderRadius: BorderRadius.only(
                              //                   topLeft: Radius.circular(8),
                              //                   topRight: Radius.circular(8),
                              //                 ),
                              //               ),
                              //               child: Row(
                              //                 children: [
                              //                   Container(
                              //                     width: 40,
                              //                     height: 40,
                              //                     decoration: BoxDecoration(
                              //                       color: Colors.white,
                              //                       shape: BoxShape.circle,
                              //                     ),
                              //                     child: Icon(
                              //                       Icons.person_outline,
                              //                       color: Colors.blue.shade700,
                              //                       size: 24,
                              //                     ),
                              //                   ),
                              //                   SizedBox(width: 12),
                              //                   Column(
                              //                     crossAxisAlignment:
                              //                         CrossAxisAlignment.start,
                              //                     children: [
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• ID: ${role.id}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                           SizedBox(width: 8),
                              //                           Text(
                              //                             '• Version: ${role.version}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                       SizedBox(height: 4),
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• Created: ${role.createdDate} by ${role.createdBy}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                       SizedBox(height: 4),
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• Last Modified: ${role.modifiedDate} by ${role.modifiedBy}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                     ],
                              //                   ),
                              //                 ],
                              //               ),
                              //             ),
                              //             // Editable Properties section
                              //             Container(
                              //               padding: EdgeInsets.all(12),
                              //               child: Column(
                              //                 crossAxisAlignment:
                              //                     CrossAxisAlignment.start,
                              //                 children: [
                              //                   Text(
                              //                     'Editable Properties:',
                              //                     style: TextStyle(
                              //                       fontWeight: FontWeight.bold,
                              //                       fontSize: 14,
                              //                     ),
                              //                   ),
                              //                   SizedBox(height: 8),
                              //                   Text(
                              //                     '• Use Cases:',
                              //                     style: TextStyle(
                              //                       fontWeight: FontWeight.w500,
                              //                       fontSize: 13,
                              //                     ),
                              //                   ),
                              //                   if (role.useCases != null)
                              //                     ...role.useCases!
                              //                         .map((useCase) => Padding(
                              //                               padding:
                              //                                   EdgeInsets.only(
                              //                                       left: 12,
                              //                                       top: 4),
                              //                               child: Text(
                              //                                 useCase,
                              //                                 style: TextStyle(
                              //                                     fontSize: 13),
                              //                               ),
                              //                             )),
                              //                 ],
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //       ),
                              //     ),
                              //     preferBelow: true,
                              //     verticalOffset: 0,
                              //     padding: EdgeInsets.zero,
                              //     margin: EdgeInsets.zero,
                              //     showDuration: Duration(seconds: 10),
                              //     decoration: BoxDecoration(
                              //       color: Colors.transparent,
                              //       boxShadow: [],
                              //     ),
                              //     child: RichText(
                              //       text: TextSpan(
                              //         children: [
                              //           TextSpan(
                              //             text: role.title,
                              //             style: TextStyle(
                              //               fontSize: 14,
                              //               fontWeight: FontWeight
                              //                   .bold, // Bold for role title
                              //               color: Colors.black,
                              //               fontFamily: "TiemposText",
                              //             ),
                              //           ),
                              //           TextSpan(
                              //             text:
                              //                 ' has the following permissions:',
                              //             style: TextStyle(
                              //               fontSize: 14,
                              //               fontWeight: FontWeight
                              //                   .normal, // Normal for the rest of text
                              //               color: Colors.black,
                              //               fontFamily: "TiemposText",
                              //             ),
                              //           ),
                              //         ],
                              //       ),
                              //     ),
                              //   ),
                              // ),

                              SizedBox(height: 12),

                              // Entity permissions
                              ...(() {
                                // Get entity permissions directly from the role
                                List<String> entityPermissions = [];

                                if (role.permissions?['entities'] != null) {
                                  entityPermissions = role
                                      .permissions!['entities'] as List<String>;
                                }

                                // If still empty, use default values
                                if (entityPermissions.isEmpty) {
                                  entityPermissions = [
                                    'Can Read entity Policy',
                                    'Can Read entity Customer',
                                    'Can Read, Create, Update, Delete entity Claim',
                                    'Can Read, Create, Update, Delete entity ClaimNote',
                                    'Can Read, Create, Update, Delete entity ClaimDocument',
                                    'Can Read, Create, Update, Delete entity ClaimPayment',
                                    'Can Read entity Coverage',
                                  ];
                                }

                                return entityPermissions;
                              }())
                                  .map((permission) => Padding(
                                        padding: EdgeInsets.only(
                                            // bottom: 2,
                                            left: 8),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('• ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  // height: 0.5,
                                                )),
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  // Get the render box of the text
                                                  // final RenderBox textBox =
                                                  //     context.findRenderObject()
                                                  //         as RenderBox;
                                                  // Get the global position of the text
                                                  // final Offset textPosition =
                                                  //     textBox.localToGlobal(
                                                  //         Offset.zero);

                                                  // Show dialog
                                                  showDialog(
                                                    context: context,
                                                    barrierColor:
                                                        Colors.transparent,
                                                    builder:
                                                        (BuildContext context) {
                                                      return Stack(
                                                        children: [
                                                          Positioned(
                                                            right: 30,
                                                            top: 130,
                                                            child: Dialog(
                                                              backgroundColor:
                                                                  Colors.white,
                                                              elevation: 8,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  border: Border.all(
                                                                      color: const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          94,
                                                                          162,
                                                                          192)),
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          AppSpacing
                                                                              .md),
                                                                ),
                                                                constraints:
                                                                    BoxConstraints(
                                                                  maxHeight:
                                                                      300,
                                                                  minHeight:
                                                                      200,
                                                                ),
                                                                width: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .width /
                                                                    3.8,
                                                                padding:
                                                                    EdgeInsets.all(
                                                                        AppSpacing
                                                                            .xs),
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Flexible(
                                                                      child:
                                                                          TextField(
                                                                        controller:
                                                                            chatController,
                                                                        maxLines:
                                                                            null,
                                                                        decoration:
                                                                            InputDecoration(
                                                                          focusColor:
                                                                              Colors.transparent,
                                                                          hintText:
                                                                              "You want to make a comment on ${role.title} permissions? Please put your points in details I will help you to integrate it.",
                                                                          hintStyle:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                14,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            color:
                                                                                Colors.grey,
                                                                            fontFamily:
                                                                                "TiemposText",
                                                                          ),
                                                                          hoverColor:
                                                                              Colors.transparent,
                                                                          border:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          enabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          focusedBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          errorBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          disabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                        ),
                                                                        onSubmitted:
                                                                            (_) =>
                                                                                _sendMessage(),
                                                                      ),
                                                                    ),
                                                                    SizedBox(
                                                                        height:
                                                                            40),
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .end,
                                                                      children: [
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.mic_rounded),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.attachment),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.add),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                          icon:
                                                                              Icon(Icons.arrow_upward),
                                                                          onPressed:
                                                                              _sendMessage,
                                                                        ),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                                child: Text(
                                                  permission,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontFamily: "TiemposText",
                                                    height: 1.5,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),

                              SizedBox(height: 12),

                              // Objectives
                              Padding(
                                padding: const EdgeInsets.only(left: 7.0),
                                child: Text(
                                  '• Can Execute objectives:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: "TiemposText",
                                  ),
                                ),
                              ),
                              SizedBox(height: 2),

                              ...(() {
                                // Get objective permissions directly from the role
                                List<String> objectivePermissions = [];

                                if (role.permissions?['objectives'] != null) {
                                  objectivePermissions =
                                      role.permissions!['objectives']
                                          as List<String>;
                                }

                                // If still empty, use default values
                                if (objectivePermissions.isEmpty) {
                                  objectivePermissions = [
                                    'Create Claim',
                                    'Assign Claim',
                                    'Review Claim',
                                    'Approve Claim',
                                    'Reject Claim',
                                    'Close Claim',
                                    'Initiate Investigation',
                                    'Assign Investigator',
                                    'Collect Evidence',
                                  ];
                                }

                                return objectivePermissions;
                              }())
                                  .map((objective) => Padding(
                                        padding: EdgeInsets.only(
                                            // bottom: 2,
                                            left: 12),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('- ',
                                                style: TextStyle(
                                                    color:
                                                        Colors.grey.shade600)),
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  // Get the render box of the text
                                                  // final RenderBox textBox =
                                                  //     context.findRenderObject()
                                                  //         as RenderBox;
                                                  // // Get the global position of the text
                                                  // final Offset textPosition =
                                                  //     textBox.localToGlobal(
                                                  //         Offset.zero);

                                                  // Show dialog
                                                  showDialog(
                                                    context: context,
                                                    barrierColor:
                                                        Colors.transparent,
                                                    builder:
                                                        (BuildContext context) {
                                                      return Stack(
                                                        children: [
                                                          Positioned(
                                                            right: 30,
                                                            top: 130,
                                                            child: Dialog(
                                                              backgroundColor:
                                                                  Colors.white,
                                                              elevation: 8,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  border: Border.all(
                                                                      color: const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          94,
                                                                          162,
                                                                          192)),
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          AppSpacing
                                                                              .md),
                                                                ),
                                                                constraints:
                                                                    BoxConstraints(
                                                                  maxHeight:
                                                                      300,
                                                                  minHeight:
                                                                      200,
                                                                ),
                                                                width: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .width /
                                                                    3.8,
                                                                padding:
                                                                    EdgeInsets.all(
                                                                        AppSpacing
                                                                            .xs),
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Flexible(
                                                                      child:
                                                                          TextField(
                                                                        controller:
                                                                            chatController,
                                                                        maxLines:
                                                                            null,
                                                                        decoration:
                                                                            InputDecoration(
                                                                          focusColor:
                                                                              Colors.transparent,
                                                                          hintText:
                                                                              "You want to make a comment on ${role.title} objectives? Please put your points in details I will help you to integrate it.",
                                                                          hintStyle:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                14,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            color:
                                                                                Colors.grey,
                                                                            fontFamily:
                                                                                "TiemposText",
                                                                          ),
                                                                          hoverColor:
                                                                              Colors.transparent,
                                                                          border:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          enabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          focusedBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          errorBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          disabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                        ),
                                                                        onSubmitted:
                                                                            (_) =>
                                                                                _sendMessage(),
                                                                      ),
                                                                    ),
                                                                    SizedBox(
                                                                        height:
                                                                            40),
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .end,
                                                                      children: [
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.mic_rounded),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.attachment),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.add),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                          icon:
                                                                              Icon(Icons.arrow_upward),
                                                                          onPressed:
                                                                              _sendMessage,
                                                                        ),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                                child: Text(
                                                  objective,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontFamily: "TiemposText",
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]));
  }

  Widget _buildEntityDetailsPanel(Entity entity) {
    GlobalKey statefulBuilderKey = GlobalKey();
    double bottomSpacerHeight = 500;
    bool isProgrammaticScroll = false;
    bool hasUserScrolled = false;

    // Define the sections for navigation with their abbreviations
    final Map<String, String> sectionMap = {
      'attributes': 'ATT',
      'business_rules': 'BR',
      'relationships': 'REL',
      'circuit_board': 'CB',
      'constants_validations': 'CV',
      'agents': 'AG',
      'workflow': 'WF',
    };

    // Create a scroll controller to handle scrolling to sections
    final ScrollController scrollController = ScrollController();

    // Keys for each section to enable scrolling to them
    final Map<String, GlobalKey> sectionKeys = {
      for (final key in sectionMap.keys) key: GlobalKey(),
    };
    scrollController.addListener(() {
      if (!isProgrammaticScroll && !hasUserScrolled) {
        statefulBuilderKey.currentState?.setState(() {
          hasUserScrolled = true;
          bottomSpacerHeight = 100;
        });
        // setState(() {
        //   hasUserScrolled = true;
        //   bottomSpacerHeight = 100; // Remove extra space when user scrolls
        // });
      }

      // isProgrammaticScroll = false;
    });

    // Function to scroll to a specific section
    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final renderObject =
            sectionKeys[sectionId]?.currentContext?.findRenderObject();
        if (renderObject != null) {
          final sectionIndex = sectionMap.keys.toList().indexOf(sectionId);

          statefulBuilderKey.currentState?.setState(() {
            isProgrammaticScroll = true;
            hasUserScrolled = false;
            selectedSectionIndex = sectionIndex;

            // Calculate required bottom padding based on section position
            if (sectionIndex >= 4) {
              // Last 3 sections (CV=4, AG=5, WF=6)
              bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              bottomSpacerHeight = 500;
            }
          });

          // Delay scroll to ensure padding is applied
          Future.delayed(Duration(milliseconds: 100), () {
            scrollController.position
                .ensureVisible(
              renderObject,
              alignment: 0.0,
              duration: Duration(milliseconds: 600),
              curve: Curves.easeInOut,
            )
                .then((_) {
              // Reset flag after animation completes
              isProgrammaticScroll = false;
            });
          });
        }
      }
    }

    return StatefulBuilder(
        key: statefulBuilderKey,
        builder: (context, innerSetState) {
          return Container(
            width: 400,
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with entity title and close button
                Container(
                  padding: const EdgeInsets.only(
                      left: AppSpacing.xxl,
                      right: AppSpacing.xxs,
                      bottom: AppSpacing.xxs,
                      top: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const CircleAvatar(
                              backgroundColor: Color(0xffE6F7FF),
                              radius: 10,
                              child: Icon(
                                Icons.person_outline,
                                color: Color(0xff1890FF),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: AppSpacing.xs),
                            Expanded(
                              child: Text(
                                entity.title ?? 'Entity Details',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                  fontFamily: 'TimeposText',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.chat),
                        onPressed: () {},
                        iconSize: 20,
                      ),
                      OcrPanelToggleButton(
                        isShown: showOcrPanel || showSidePanel,
                        onToggle: toggleOcrPanel,
                      ),
                    ],
                  ),
                ),

                // Navigation and content
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left navigation
                      Container(
                        width: 40,
                        padding: const EdgeInsets.only(top: 3),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            for (int i = 0; i < sectionMap.length; i++)
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 8, top: i == 0 ? 16 : 10),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToSection(
                                        sectionMap.keys.elementAt(i)),
                                    child: Text(
                                      sectionMap.values.elementAt(i),
                                      style: TextStyle(
                                        fontWeight: selectedSectionIndex == i
                                            ? FontWeight.bold
                                            : FontWeight.w500,
                                        fontSize: 10,
                                        color: selectedSectionIndex == i
                                            ? Colors.black
                                            : Colors.grey.shade700,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Right content area with scrollable sections
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return SingleChildScrollView(
                              controller: scrollController,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  for (final sectionId in sectionMap.keys)
                                    _buildEntitySectionWithKey(
                                      sectionId,
                                      entity,
                                      sectionKeys,
                                    ),

                                  // Smart bottom padding
                                  SizedBox(height: bottomSpacerHeight),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  // Build an entity section with a key for scrolling
  Widget _buildEntitySectionWithKey(
      String sectionId, Entity entity, Map<String, GlobalKey> sectionKeys) {
    return Container(
      key: sectionKeys[sectionId],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Container(
            padding: const EdgeInsets.only(
                left: AppSpacing.sm, right: AppSpacing.sm, top: AppSpacing.md),
            color: Colors.white,
            child: Text(
              _getSectionTitle(sectionId),
              style: const TextStyle(
                fontSize: 10,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.only(
                left: AppSpacing.md, right: AppSpacing.md),
            child: _buildContentForEntitySection(sectionId, entity),
          ),
        ],
      ),
    );
  }

  // Get section title from section ID
  String _getSectionTitle(String sectionId) {
    switch (sectionId) {
      case 'attributes':
        return 'Attributes';
      case 'business_rules':
        return 'Business Rules';
      case 'relationships':
        return 'Relationships';
      case 'circuit_board':
        return 'Circuit Board';
      case 'constants_validations':
        return 'Constants & Validations';
      case 'agents':
        return 'Agents';
      case 'workflow':
        return 'Workflows';
      default:
        return 'Section';
    }
  }

  // Build content for entity section based on API data
  Widget _buildContentForEntitySection(String sectionId, Entity entity) {
    // Special handling for attributes section - use entity's attributes data
    if (sectionId == 'attributes') {
      return _buildAttributesFromEntityData(entity);
    }

    // Try to get section data from API if available
    EntitySection? apiSection = _getEntitySectionFromApi(sectionId, entity);

    if (apiSection != null) {
      return _buildApiBasedSectionContent(apiSection);
    }

    // Only display content when it comes from the API - no fallback
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  // Build attributes section from entity's attributes data
  Widget _buildAttributesFromEntityData(Entity entity) {
    // Look for the EntityElement in our global storage
    if (entity.id != null && globalEntityElements.containsKey(entity.id!)) {
      EntityElement entityElement = globalEntityElements[entity.id!]!;

      // Check if this entity has attributes data from API
      if (entityElement.attributes != null &&
          entityElement.attributes!.isNotEmpty) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (final attribute in entityElement.attributes!)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '${attribute.name}: ${attribute.description} (${attribute.type})',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      }
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  // Get entity section from API data
  EntitySection? _getEntitySectionFromApi(String sectionId, Entity entity) {
    // Look for the EntityElement in our global storage
    if (entity.id != null && globalEntityElements.containsKey(entity.id!)) {
      EntityElement entityElement = globalEntityElements[entity.id!]!;

      // Check if this entity has sections data from API
      if (entityElement.sections != null &&
          entityElement.sections!.isNotEmpty) {
        // Find the section by ID or abbreviation
        for (var section in entityElement.sections!) {
          // Match by section ID or abbreviation
          if (section.id == sectionId ||
              section.abbreviation ==
                  _getSectionAbbreviationFromId(sectionId)) {
            return section;
          }
        }
      }
    }

    // Return null if no API data is available - no fallback
    return null;
  }

  // Get section abbreviation from section ID
  String _getSectionAbbreviationFromId(String sectionId) {
    switch (sectionId) {
      case 'attributes':
        return 'ATT';
      case 'business_rules':
        return 'BR';
      case 'relationships':
        return 'REL';
      case 'circuit_board':
        return 'CB';
      case 'constants_validations':
        return 'CV';
      case 'agents':
        return 'AG';
      case 'workflow':
        return 'WF';
      default:
        return sectionId.toUpperCase();
    }
  }

  // Build section content based on API data
  Widget _buildApiBasedSectionContent(EntitySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display section content
        if (section.content != null && section.content!.isNotEmpty)
          Text(
            section.content!,
            style: const TextStyle(fontSize: 14, fontFamily: 'TiemposText'),
          ),

        const SizedBox(height: AppSpacing.sm),

        // Display tabs if available
        if (section.tabs != null && section.tabs!.isNotEmpty)
          _buildTabsForSection(section)
        // Display entity data if available (for Data section)
        else if (section.entityData != null && section.entityData!.isNotEmpty)
          _buildEntityDataSection(section)
        // Display section items if no tabs or entity data
        else if (section.items != null && section.items!.isNotEmpty)
          for (final item in section.items!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: _buildSectionItem(item),
            ),
      ],
    );
  }

  // Build entity data section (for Data section)
  Widget _buildEntityDataSection(EntitySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (final dataGroup in section.entityData!)
          if (dataGroup.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (final dataItem in dataGroup)
                  Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.xxs),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '• ',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                        Expanded(
                          child: Text(
                            '${dataItem.name}: ${dataItem.value}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: AppSpacing.sm),
              ],
            ),
      ],
    );
  }

  // Build tabs for a section
  Widget _buildTabsForSection(EntitySection section) {
    // Get the selected tab for this section
    String? selectedTab = selectedEntityTabs[section.id];

    // If no tab is selected, select the first one or the one marked as selected
    if (selectedTab == null && section.tabs!.isNotEmpty) {
      // Look for a tab marked as selected
      for (int i = 0; i < section.tabs!.length; i++) {
        var tab = section.tabs![i];
        if (tab.isSelected == true) {
          selectedTab = tab.name ?? i.toString();
          selectedEntityTabs[section.id!] = selectedTab;
          break;
        }
      }

      // If no tab is marked as selected, use the first tab
      if (selectedTab == null) {
        selectedTab = section.tabs!.first.name ?? '0';
        selectedEntityTabs[section.id!] = selectedTab;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab buttons
        SizedBox(
          height: 40,
          child: Row(
            children: [
              for (int i = 0; i < section.tabs!.length; i++)
                _buildEntityTabButton(
                  section.id!,
                  section.tabs![i].name ?? i.toString(),
                  section.tabs![i].name ?? 'Tab ${i + 1}',
                ),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.sm),

        // Tab content
        _buildTabContent(section, selectedTab!),
      ],
    );
  }

  // Build tab button
  Widget _buildEntityTabButton(
      String sectionId, String tabId, String tabTitle) {
    final isSelected = selectedEntityTabs[sectionId] == tabId;

    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () {
            setState(() {
              selectedEntityTabs[sectionId] = tabId;
            });
          },
          child: Text(
            tabTitle,
            style: TextStyle(
              fontSize: 13,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              color: isSelected ? Colors.black : Colors.grey.shade600,
              fontFamily: 'TiemposText',
              decoration:
                  isSelected ? TextDecoration.underline : TextDecoration.none,
            ),
          ),
        ),
      ),
    );
  }

  // Build tab content
  Widget _buildTabContent(EntitySection section, String selectedTabId) {
    // Find the selected tab
    var selectedTab = section.tabs!.firstWhere(
      (tab) => tab.name == selectedTabId,
      orElse: () => section.tabs!.first,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display tab data/items
        if (selectedTab.data != null && selectedTab.data!.isNotEmpty)
          for (final item in selectedTab.data!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xxs),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '• ',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: Colors.black,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ],
    );
  }

  // Build individual section item based on its type
  Widget _buildSectionItem(dynamic item) {
    // If item is a Map (structured data like business rules)
    if (item is Map<String, dynamic>) {
      return _buildStructuredItem(item);
    }

    // If item is a simple string, check if it already has a bullet point
    String itemText = item.toString();
    bool alreadyHasBullet = itemText.startsWith('• ');

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Only add bullet if the item doesn't already have one
        if (!alreadyHasBullet)
          const Text(
            '• ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        Expanded(
          child: Text(
            itemText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        ),
      ],
    );
  }

  // Build structured item (like business rules with name, description, severity)
  Widget _buildStructuredItem(Map<String, dynamic> item) {
    // Build a simple text representation without card styling
    String displayText = '';

    // Handle Constants & Validations rule field
    if (item['rule'] != null) {
      displayText = item['rule'];
    }
    // Handle Business Rules fields
    else if (item['name'] != null || item['description'] != null) {
      if (item['name'] != null) {
        displayText += item['name'];
      }

      if (item['description'] != null) {
        if (displayText.isNotEmpty) displayText += ': ';
        displayText += item['description'];
      }

      if (item['severity'] != null) {
        if (displayText.isNotEmpty) displayText += ', ';
        displayText += 'Severity: ${item['severity']}';
      }
    }
    // Handle Workflow items with title and data
    else if (item['title'] != null) {
      displayText = item['title'];
      if (item['data'] != null && item['data'] is List) {
        displayText += ' (${(item['data'] as List).join(', ')})';
      }
    }

    // Add other fields if available
    if (item['entity'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Entity: ${item['entity']}';
    }

    if (item['field'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Field: ${item['field']}';
    }

    if (item['type'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Type: ${item['type']}';
    }

    if (item['via'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Via: ${item['via']}';
    }

    // Check if the text already has a bullet point
    bool alreadyHasBullet = displayText.startsWith('• ');

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Only add bullet if the text doesn't already have one
        if (!alreadyHasBullet)
          const Text(
            '• ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        Expanded(
          child: Text(
            displayText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to build section headers
  Widget _buildSectionHeader(String title, {Key? key}) {
    return Container(
      key: key,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(color: Colors.white),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: 'TiemposText',
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build attributes section
  Widget _buildAttributesSection(Entity entity) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Customer ID
          _buildAttributeDetail(
            'Customer ID',
            'Data type is String (alphanumeric with maximum 100 characters). Error message: "Customer ID can only contain letters, numbers and spaces." Mandatory field.',
          ),

          // Name
          _buildAttributeDetail(
            'Name',
            'Data Type is String (100 characters). Error message: "Name cannot exceed 100 characters." Mandatory field.',
          ),

          // Email
          _buildAttributeDetail(
            'Email',
            'Data Type is String (Must follow valid email format). Error message: "Please enter a valid email address." Mandatory field.',
          ),

          // Phone Number
          _buildAttributeDetail(
            'Phone Number',
            'Data Type is String, Mandatory field. Must contain only digits and symbols + - ( ). Error message: "Phone number format is incorrect."',
          ),

          // Address
          _buildAttributeDetail(
            'Address',
            'Data Type is String, Optional No specific validation rules.',
          ),

          // Gender
          _buildAttributeDetail(
            'Gender',
            'Data Type is String, Optional Must be one of: Male, Female, Other, Prefer not to say. Error message: "Please select a valid gender option."',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single attribute detail
  Widget _buildAttributeDetail(String name, String description) {
    // Extract data type for the tooltip
    String dataType = "String";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 8),
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                height: 1.4, // Increased line height for better readability
              ),
              children: [
                WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: Tooltip(
                      richMessage: WidgetSpan(
                        child: Container(
                          margin: EdgeInsets.only(left: 230),
                          width: 300,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withValues(alpha: 51), // 0.2 * 255 = ~51
                                blurRadius: 10,
                                offset: Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Blue header with icon
                              Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade700,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    topRight: Radius.circular(8),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.folder,
                                        color: Colors.blue.shade700,
                                        size: 24,
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          name,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              // Attribute Properties section
                              Container(
                                padding: EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          '• ID : at001',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          '• Version :V00012',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          '• Data Type: $dataType',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• Created : 12/04/2025 by John Smith',
                                      style: TextStyle(
                                        fontSize: 11,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• Last Modified : 12/04/2025 by John Smith',
                                      style: TextStyle(
                                        fontSize: 11,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      preferBelow: false,
                      verticalOffset: 20,
                      padding: EdgeInsets.zero,
                      margin: EdgeInsets.zero,
                      showDuration: Duration(seconds: 10),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        boxShadow: [],
                      ),
                      child: Text(
                        "$name: ",
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
                TextSpan(
                  text: description,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ),
        // Add divider after each attribute
        Divider(
          color: Colors.grey.shade200,
          height: 16, // Total height including the divider and spacing
          thickness: 1, // Thickness of the divider line
        ),
      ],
    );
  }

  // Helper method to build business rules section
  Widget _buildBusinessRulesSection(Entity entity) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Policy Budget Rule
          _buildBusinessRule(
            'Policy Budget Rule',
            'Policies with Premium over \$1M require executive approval, Severity: Error',
          ),

          // Policy Timeline Rule
          _buildBusinessRule(
            'Policy Timeline Rule',
            'End date must be after start date, Severity: Error',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single business rule
  Widget _buildBusinessRule(String name, String description) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build relationships section
  Widget _buildRelationshipsSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // One-to-many with Order
          _buildRelationship(
            'One-to-many with',
            'Order',
            'via',
            'Customer ID',
          ),

          // One-to-one with Customer Preference
          _buildRelationship(
            'One-to-one with',
            'Customer Preference',
            'via',
            'Customer ID',
          ),

          // Many-to-many with Product
          _buildRelationship(
            'Many-to-many with',
            'Product',
            'via',
            'Wishlist Item',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single relationship
  Widget _buildRelationship(
      String type, String entity, String via, String field) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  type,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 4),
                Text(
                  entity,
                  style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black),
                ),
                SizedBox(width: 4),
                Text(
                  via,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 4),
                field == "Customer ID"
                    ? _buildFieldWithTooltip(field)
                    : Text(
                        field,
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontFamily: 'TiemposText',
                            fontSize: 14,
                            color: Colors.black),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build circuit board section
  Widget _buildCircuitBoardSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tabs
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCircuitTab(
                    'Synthetic', selectedCircuitTab == 'Synthetic'),
                _buildCircuitTab(
                    'Classification', selectedCircuitTab == 'Classification'),
                _buildCircuitTab(
                    'Distribution', selectedCircuitTab == 'Distribution'),
                _buildCircuitTab('Loading Strategy',
                    selectedCircuitTab == 'Loading Strategy'),
              ],
            ),
          ),
          SizedBox(height: 16),

          // Name, Status And Email
          Tooltip(
            message:
                'Generates realistic test data for development and testing',
            preferBelow: false, // show above the text
            verticalOffset: 10, // reduce to e.g., 4 for minimal gap
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(2, 2),
                )
              ],
            ),
            textStyle: TextStyle(
              color: Colors.black,
              fontSize: 13,
            ),
            padding: EdgeInsets.all(8),
            child: Text(
              'Name, Status And Email',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),

          SizedBox(height: 12),

          // Public
          _buildCircuitCategory('Public:', 'Name, Status, Email'),

          // Internal
          _buildCircuitCategory('Internal:', 'Phone Number, Address'),

          // Confidential
          _buildCircuitCategory('Confidential:', 'Date Of Birth, Age'),

          // Restricted
          _buildCircuitCategory('Restricted:', 'Customer Id'),
        ],
      ),
    );
  }

  // Helper method to build circuit board tab
  Widget _buildCircuitTab(String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCircuitTab = label;
        });
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: IntrinsicWidth(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.grey.shade700,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 13,
                  ),
                ),
                SizedBox(height: 4),
                Container(
                  height: 2,
                  color: isSelected ? Colors.black : Colors.transparent,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build circuit category
  Widget _buildCircuitCategory(String category, String fields) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            category,
            style: TextStyle(
              fontFamily: 'TiemposText',
              fontSize: 14,
            ),
          ),
          SizedBox(width: 4),
          Expanded(
            child: Text(
              fields,
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build constants and validations section
  Widget _buildConstantsAndValidationsSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Max Length For Name
          _buildValidation('Max Length For Name Is 100'),

          // Default Value For Status
          _buildValidation('Default Value For Status Is "Pending"'),
        ],
      ),
    );
  }

  // Helper method to build a single validation rule
  Widget _buildValidation(String rule) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                fontFamily: 'TiemposText',
              )),
          Expanded(
            child: Text(
              rule,
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to check if text would be truncated with ellipsis
  bool _wouldTextBeTruncated(String text, TextStyle style, double maxWidth) {
    // First, measure the actual width of the text without constraints
    final TextPainter measuringPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    // Get the actual width of the text
    final double actualTextWidth = measuringPainter.width;

    // Removed verbose text measurement logging

    // Return true if the actual width exceeds the maximum width
    return actualTextWidth > maxWidth;
  }

  // Helper method to build entity card
  Widget _buildEntityCard(
      Entity entity, List<Entity> allEntities, EntityGroup group) {
    // If this is a relationship entity, hide it
    if (entity.relationType != null) {
      return SizedBox.shrink();
    }

    // If this is the first entity and we're showing related entities, highlight it
    // (No need to show a snackbar here as it's already shown in the click handler)

    final bool isSelected =
        selectedEntity != null && selectedEntity!.id == entity.id;

    // No tooltip highlighting needed

    // Create a unique global key for this entity card
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'entityCard_${entity.id}');

    // Check if the entire title row would be truncated
    bool wouldBeTruncated = false;

    double availableWidth = MediaQuery.of(context).size.width / 1.55;
    // availableWidth = availableWidth -
    //     (showSidePanel ? sidePanelWidth : 0) -
    //     (isChatHistoryExpanded ? MediaQuery.of(context).size.width / 6 : 0) -
    //     100;

    // Create a combined string of the entity title and attributes for truncation check
    String fullTitleText = entity.title ?? 'Untitled';
    if (entity.attributeString != null && entity.attributeString!.isNotEmpty) {
      fullTitleText += ' has ${entity.attributeString!}';
    }

    // Check if the combined text would be truncated
    wouldBeTruncated = _wouldTextBeTruncated(
        fullTitleText,
        TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        availableWidth);

    // Removed verbose entity text logging

    // If there are related entities, we should use expansion tile regardless
    final bool shouldUseExpansionTile = wouldBeTruncated;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(0),
          decoration: BoxDecoration(
            color: isSelected ? Color(0xFFE3F2FD) : Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade200),
              left: isSelected
                  ? BorderSide(color: Colors.blue.shade700, width: 4)
                  : BorderSide.none,
            ),
          ),
          child: Column(
            children: [
              //    shouldUseExpansionTile
              // ?
              CustomExpansionTileWithEllipsis(
                entity: entity,
                entityCardKey: entityCardKey,
                onExpansionChanged: (expanded) {
                  setState(() {
                    // Update the local entity for immediate UI response
                    entity.expanded = expanded;

                    // Update the entity's expanded state in the global data
                    globalEntitiesData.updateEntityExpandedState(
                        entity.id ?? '', expanded);
                  });
                },
                backgroundColor: Colors.transparent,
                children: [],
                onThreeDotsPressed: () {
                  // Handle three dots menu action for entity
                  print('Three dots pressed for entity: ${entity.title}');
                  // You can add your custom logic here, such as:
                  // - Show context menu
                  // - Open entity options dialog
                  // - Navigate to entity details
                  // - etc.
                },
                onTitleTap: () {
                  // Update last click time to prevent tooltip from showing
                  _lastClickTime = DateTime.now();

                  // Hide any existing tooltip when clicking
                  hideProfileTooltip();

                  setState(() {
                    if (selectedEntity != null &&
                        selectedEntity!.id == entity.id) {
                      // Keep selected
                    } else {
                      selectedRole = null;
                      selectedEntity = entity;
                      selectedWorkflow = null;
                    }
                    showSidePanel = true;
                    selectedSectionIndex = 0;
                  });
                },
              )
              //     : GestureDetector(
              //         onTap: () {
              //           // Update last click time to prevent tooltip from showing
              //           _lastClickTime = DateTime.now();

              //           // Hide any existing tooltip when clicking
              //           hideProfileTooltip();

              //           setState(() {
              //             if (selectedEntity != null &&
              //                 selectedEntity!.id == entity.id) {
              //               // Keep selected
              //             } else {
              //               selectedRole = null;
              //               selectedEntity = entity;
              //               selectedWorkflow = null;
              //             }
              //             showSidePanel = true;
              //             selectedSectionIndex = 0;
              //           });
              //         },
              //         child: Padding(
              //           padding:
              //               EdgeInsets.symmetric(horizontal: AppSpacing.xs),
              //           child: entityTitleWidget(entityCardKey, entity),
              //         ),
              //       ),

              // // No relationship entities shown
            ],
          ),
        ),
      ],
    );
  }

  Widget entityTitleWidget(entityCardKey, entity, {allowEllipsis = true}) {
    // Removed verbose entity title logging

    // Get the latest entity state from the global data
    Entity currentEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              currentEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    allowEllipsis = !(currentEntity.expanded ?? false);

    return Row(
      key: entityCardKey,
      children: [
        // Container(
        //   padding: EdgeInsets.only(
        //       left: 0,
        //       right: AppSpacing.xxs,
        //       top: AppSpacing.xs,
        //       bottom: AppSpacing.xs),
        //   decoration: BoxDecoration(
        //     border: Border(
        //         right: BorderSide(color: Colors.grey.shade300, width: 1)),
        //   ),
        //   child: CustomCheckbox(
        //     initialValue:
        //         globalEntitiesData.getEntityCheckedState(entity.id ?? ''),
        //     onChanged: (bool value) {
        //       setState(() {
        //         // Update the entity's checked state in the global data
        //         globalEntitiesData.updateEntityCheckedState(
        //             entity.id ?? '', value);
        //       });
        //     },
        //     materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        //   ),
        // ),
        // SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Row(
              children: [
                // Create a unique global key for the entity title
                Builder(
                  builder: (context) {
                    final GlobalKey titleKey =
                        GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                    return MouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (_) {
                        // Show profile tooltip only when hovering on title
                        showProfileTooltip(titleKey, entity);
                      },
                      onExit: (_) {
                        // Hide profile tooltip when not hovering
                        hideProfileTooltip();
                      },
                      child: Text(
                        entity.title ?? 'Untitled',
                        key: titleKey,
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText"),
                      ),
                    );
                  },
                ),
                SizedBox(width: AppSpacing.xxs),
                if (entity.attributeString != null &&
                    entity.attributeString!.isNotEmpty)
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'has ',
                          style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText"),
                        ),
                        Expanded(
                          child: _buildAttributeStringWithPrimaryKeys(entity,
                              allowEllipsis: allowEllipsis),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build entities response
  Widget _buildEntitiesResponse() {
    // Show loading indicator while entities are being loaded
    if (isLoadingEntities) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading entities data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    // Use the already loaded entity groups from the class variable
    // No need to create a default group as we're using the loaded groups directly

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header with icon
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Container(
            //   margin: EdgeInsets.only(
            //       // right:
            //       // 30,
            //       // AppSpacing.sm,
            //       top: 2),
            //   child: Image(
            //     image: AssetImage('assets/images/checklist_small.png'),
            //     // height: 200,
            //   ),
            //   // Icon(
            //   //   Icons.description_outlined,
            //   //   size: 20,
            //   //   color: Colors.amber.shade800,
            //   // ),
            // ),
            // SizedBox(width:12),
            // Expanded(
            //   child: Consumer<AuthProvider>(
            //     builder: (context, authProvider, _) {
            //       // Get the username from the user profile
            //       final String firstName = authProvider.user?.username ?? '';

            //       // Get the localized greeting text
            //       final String greeting = AppLocalizations.of(context)
            //           .translate('build.entitiesGreeting');

            //       // If we have a username, prepend it to the greeting
            //       final String displayText =
            //           firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

            //       return Text(
            //         displayText,
            //         style: TextStyle(
            //           fontFamily: 'TiemposText',
            //           fontSize: 14,
            //           fontWeight: FontWeight.w500,
            //         ),
            //       );
            //     },
            //   ),
            // ),
            SvgPicture.asset('assets/images/icons/box.svg',
                // fit: BoxFit.fill,
                height: 24,
                width: 24),
          ],
        ),
        SizedBox(height: AppSpacing.xs),

        // Entity groups
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.xxs,
            vertical: AppSpacing.xxs,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
            border: Border.all(color: Colors.grey.shade300),
          ),
          // margin: EdgeInsets.only(left: 24), // Indent the content
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
              // padding: EdgeInsets.only(
              //   // horizontal: AppSpacing.xs,
              //   top: AppSpacing.xxs,
              //   bottom: AppSpacing.xs,
              // ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(globalEntitiesData.systemInfo?.headerText ?? '',
                      style: TextStyle(
                        fontFamily: 'TiemposText',
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      )),
                  // Custom dropdown that looks exactly like the image
                  globalEntitiesData.systemInfo?.enitiyVersions != null &&
                          (globalEntitiesData.systemInfo!.enitiyVersions!)
                              .isNotEmpty
                      ? CustomEntityVersionDropdown(
                          versions: globalEntitiesData
                              .systemInfo!.enitiyVersions!
                              .map((v) => v.toString())
                              .toList(),
                          selectedVersion:
                              globalEntitiesData.getSelectedVersion(
                                  globalEntitiesData.systemInfo?.entityName ??
                                      ''),
                          onVersionSelected: (String version) {
                            setState(() {
                              globalEntitiesData.setSelectedVersion(
                                  globalEntitiesData.systemInfo?.entityName ??
                                      '',
                                  version);
                            });
                          },
                        )
                      : Container(),
                ],
              ),
            ),
            for (var group in globalEntitiesData.entityGroups ?? [])
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Group header
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      // borderRadius: BorderRadius.only(
                      //   topLeft: Radius.circular(AppSpacing.xxs),
                      //   topRight: Radius.circular(AppSpacing.xxs),
                      // ),
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade300),
                        left: BorderSide(color: Colors.grey.shade300),
                        right: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Container(
                        //   padding: EdgeInsets.only(
                        //       left: 0,
                        //       right: AppSpacing.xxs,
                        //       top: AppSpacing.xs,
                        //       bottom: AppSpacing.xs),
                        //   decoration: BoxDecoration(
                        //     border: Border(
                        //         right: BorderSide(
                        //             color: Colors.grey.shade300, width: 1)),
                        //   ),
                        //   child: CustomCheckbox(
                        //     initialValue: globalEntitiesData
                        //         .getGroupCheckedState(group.id ?? ''),
                        //     onChanged: (bool value) {
                        //       setState(() {
                        //         // Update the group's checked state in the global data
                        //         globalEntitiesData.updateGroupCheckedState(
                        //             group.id ?? '', value);
                        //       });
                        //     },
                        //     materialTapTargetSize:
                        //         MaterialTapTargetSize.shrinkWrap,
                        //   ),
                        // ),
                        // SizedBox(width: AppSpacing.xs),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: AppSpacing.xs),
                            child: Text(
                              'Entity: ${group.title}',
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12,
                                  fontFamily: 'TiemposText'),
                            ),
                          ),
                        ),
                        // // Custom dropdown that looks exactly like the image
                        // group.enitiyVersions != null && group.enitiyVersions!.isNotEmpty
                        //     ? CustomEntityVersionDropdown(
                        //         versions: group.enitiyVersions!.map((v) => v.toString()).toList(),
                        //         selectedVersion: globalEntitiesData.getSelectedVersion(group.id ?? ''),
                        //         onVersionSelected: (String version) {
                        //           setState(() {
                        //             globalEntitiesData.setSelectedVersion(group.id ?? '', version);
                        //           });
                        //         },
                        //       )
                        //     : Container(),
                        SizedBox(width: AppSpacing.sm),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: (group.coreCount != null &&
                                    group.coreCount!.contains('Core'))
                                ? Colors.amber.shade100
                                : Color(0xffCADEB9),
                            borderRadius: BorderRadius.circular(AppSpacing.xxs),
                          ),
                          child: Text(
                            group.coreCount ?? '',
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Entity cards
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      // borderRadius: BorderRadius.only(
                      //   bottomLeft: Radius.circular(AppSpacing.sm),
                      //   bottomRight: Radius.circular(AppSpacing.sm),
                      // ),
                      color: Colors.white,
                    ),
                    child: Column(
                      children: (group.entities ?? []).map<Widget>((entity) {
                        // Create a non-nullable list for the _buildEntityCard method
                        final List<Entity> entityList = group.entities ?? [];
                        return _buildEntityCard(entity, entityList, group);
                      }).toList(),
                    ),
                  ),

                  (globalEntitiesData.entityGroups?.indexOf(group) ?? 0) <
                          (globalEntitiesData.entityGroups?.length ?? 1) - 1
                      ? SizedBox(height: AppSpacing.xl)
                      : SizedBox(height: 0),
                ],
              ),
          ]),
        ),

        // // System Info Section - Always shown at the end
        // if (globalEntitiesData.systemInfo != null)
        //   Container(
        //     margin: EdgeInsets.only(top: 10, bottom: 10),
        //     decoration: BoxDecoration(
        //       color: Colors.transparent,
        //       // borderRadius: BorderRadius.circular(AppSpacing.sm),
        //       // border: Border.all(color: Colors.grey.shade300),
        //     ),
        //     padding: EdgeInsets.only(top: AppSpacing.xs, bottom: AppSpacing.xs),
        //     child: Column(
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         // System Info Header

        //         // Bullet points
        //         ...(globalEntitiesData.systemInfo?.bulletPoints ?? [])
        //             .map((point) => Padding(
        //                   padding: EdgeInsets.only(bottom: 8),
        //                   child: Row(
        //                     crossAxisAlignment: CrossAxisAlignment.start,
        //                     children: [
        //                       Text('• ',
        //                           style: TextStyle(
        //                             fontWeight: FontWeight.bold,
        //                             color: Colors.blue.shade800,
        //                           )),
        //                       Expanded(
        //                         child: Text(
        //                           point,
        //                           style: TextStyle(
        //                             fontFamily: 'TiemposText',
        //                             fontSize: 14,
        //                             fontWeight: FontWeight.w500,
        //                             color: Colors.black,
        //                           ),
        //                         ),
        //                       ),
        //                     ],
        //                   ),
        //                 )),
        //       ],
        //     ),
        //   ),
      ],
    );
  }

  // Helper method to get relation color based on relation type
  Color getRelationColor(String? relationType) {
    switch (relationType) {
      case 'nested':
        return Colors.green.shade700; // Green
      case 'shared':
        return Colors.blue.shade700; // Blue
      case 'junction':
        return Colors.orange.shade700; // Orange
      case 'agent':
        return Colors.purple.shade700; // Purple
      case 'workflow':
        return Colors.red.shade700; // Red
      default:
        return Colors.grey.shade700; // Default
    }
  }

  // Helper method to get relation icon based on relation type
  IconData getRelationIcon(String? relationType) {
    switch (relationType) {
      case 'junction':
        return Icons.link; // Link icon for junction
      case 'nested':
        return Icons.account_tree_outlined; // Tree icon for nested
      case 'shared':
        return Icons.share; // Share icon for shared
      case 'agent':
        return Icons.person; // Person icon for agent
      case 'workflow':
        return Icons.work; // Work icon for workflow
      default:
        return Icons.data_object_outlined; // Default
    }
  }

  // Helper method to build attribute string with primary key indicators
  Widget _buildAttributeStringWithPrimaryKeys(Entity entity,
      {bool allowEllipsis = true}) {
    // Get the latest entity state from the global data
    Entity currentEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              currentEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    allowEllipsis = !(currentEntity.expanded ?? false);

    // Removed verbose attribute logging

    if (entity.attributeString == null || entity.attributeString!.isEmpty) {
      return Text('');
    }

    // If there are no attributes or no primary keys, just return the original string
    if (entity.attributes == null || entity.attributes!.isEmpty) {
      return Text(
        entity.attributeString ?? '',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        overflow: allowEllipsis ? TextOverflow.ellipsis : TextOverflow.visible,
        softWrap: !allowEllipsis,
        maxLines: allowEllipsis ? 1 : null,
      );
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in entity.attributes!) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    final String attributeString = entity.attributeString!;
    List<String> parts = [];

    // First split by "and"
    final andParts = attributeString.split(' and ');

    // Process each part split by "and"
    for (int i = 0; i < andParts.length; i++) {
      // For all parts except the last one, split by commas
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        // For the last part, just add it directly
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];

    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();

      // Check if this attribute is a primary key
      bool isPrimaryKey = false;
      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
        // Add a small space before the superscript
        textSpans.add(TextSpan(text: ' '));

        // Use WidgetSpan for better control over positioning
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5), // Move it up to look like a superscript
              child: Text(
                '^PK',
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
      }

      // Add separator (comma or "and") if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(text: ' and '));
        } else {
          textSpans.add(TextSpan(text: ', '));
        }
      }
    }

    // Get the latest entity state from the global data
    Entity latestEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              latestEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    bool shouldUseEllipsis = !(latestEntity.expanded ?? false);

    return RichText(
      text: TextSpan(children: textSpans),
      overflow:
          shouldUseEllipsis ? TextOverflow.ellipsis : TextOverflow.visible,
      softWrap: !shouldUseEllipsis,
      maxLines: shouldUseEllipsis ? 1 : null,
    );
  }

  // Build workflows response
  Widget _buildWorkflowsResponse() {
    Logger.info('Building workflow response from PROVIDER data');
    // Get the workflow data provider
    final workflowDataProvider =
        Provider.of<WorkflowDataProvider>(context, listen: true);

    // Show loading indicator while workflows are being loaded
    if (workflowDataProvider.isLoadingWorkflows) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading workflows data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Get the first workflow from the list
    final workflow = workflowDataProvider.workflows.isNotEmpty
        ? workflowDataProvider.workflows[0]
        : null;
    if (workflow == null) {
      return Center(
        child: Text(
          'No workflow data available.',
          style: TextStyle(
            fontFamily: 'TiemposText',
            fontSize: 16,
          ),
        ),
      );
    }

    // Get the tree data from the API response
    final List<dynamic> treeData = workflow['tree'] ?? [];
    final String mainTitle = workflow['mainTitle'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with workflow info
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  // Get the username from the user profile
                  final String firstName = authProvider.user?.username ?? '';

                  // Get the localized greeting text
                  final String greeting = AppLocalizations.of(context)
                      .translate('build.workflowGreeting');

                  // If we have a username, prepend it to the greeting
                  final String displayText =
                      firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

                  return Text(
                    displayText,
                    style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            SvgPicture.asset('assets/images/eye.svg', height: 14, width: 18),
            SizedBox(width: 5),
            Text(
              'PREVIEW UI',
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 10),
            SvgPicture.asset('assets/images/icons/box.svg',
                height: 24, width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.md),

        // Workflow visualization container
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            color: Colors.white,
          ),
          padding: EdgeInsets.all(AppSpacing.xxs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workflow header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSpacing.xs),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Workflow",
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontSize: 12.0,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(Icons.folder_outlined,
                        size: 18, color: Colors.grey.shade600),
                    Icon(Icons.arrow_drop_down,
                        size: 18, color: Colors.grey.shade600),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              // Workflow title
              Padding(
                padding: EdgeInsets.only(left: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Builder(builder: (context) {
                      final GlobalKey titleKey = GlobalKey(
                          debugLabel: 'workflowTitle_${workflow['id']}');

                      return InkWell(
                        onTap: () {
                          // Update last click time to prevent tooltip from showing
                          _lastClickTime = DateTime.now();

                          // Hide any existing tooltip when clicking
                          _hideWorkflowProfileTooltip();

                          setState(() {
                            selectedRole = null;
                            selectedEntity = null;
                            selectedWorkflow = workflow;
                            showSidePanel = true;

                            // Scroll the workflow tree horizontally to show content that might be overflowing
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (_workflowTreeScrollController.hasClients) {
                                _workflowTreeScrollController.animateTo(
                                  0, // Scroll to position 0 (leftmost content)
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            });
                          });
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          onEnter: (_) {
                            // Only show tooltip if not already showing side panel for this workflow
                            if (!showSidePanel ||
                                selectedWorkflow != workflow) {
                              _showWorkflowProfileTooltip(titleKey, workflow);
                            }
                          },
                          onExit: (_) {
                            // Hide profile tooltip when not hovering
                            _hideWorkflowProfileTooltip();
                          },
                          child: Text(
                            mainTitle,
                            key: titleKey,
                            style: TextStyle(
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.md),

              // Workflow tree visualization using WorkflowTreeBuilder
              Padding(
                padding: EdgeInsets.all(AppSpacing.xs),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: _workflowTreeScrollController,
                  child: WorkflowTreeBuilder.buildWorkflowTree(treeData),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
      ],
    );
  }

  // Vertical dotted connector line for Alt 1 branch

  // Build a horizontal workflow step (for the first row)
  Widget _buildHorizontalWorkflowStep(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14,
        fontFamily: 'TiemposText',
        fontWeight: FontWeight.w500,
      ),
    );
  }

  // Build a row for parallel branch steps - new design matching the reference image
  Widget _buildParallelBranchRowNew(Map<String, dynamic> parallelChild) {
    // Get the child nodes for this branch
    final List<Map<String, dynamic>> branchNodes = [];

    // Add the level 1 node (Inventory Manager or Finance Officer)
    branchNodes.add(parallelChild);

    // Add level 2 node if it exists (System Completes Purchase Order)
    if (parallelChild.containsKey('children') &&
        parallelChild['children'] != null &&
        (parallelChild['children'] as List).isNotEmpty) {
      final level2Node =
          (parallelChild['children'] as List)[0] as Map<String, dynamic>;
      branchNodes.add(level2Node);

      // Add level 3 node if it exists (Vendor Receives Purchase Order or Delivery Receives Pickup Order)
      if (level2Node.containsKey('children') &&
          level2Node['children'] != null &&
          (level2Node['children'] as List).isNotEmpty) {
        final level3Node =
            (level2Node['children'] as List)[0] as Map<String, dynamic>;
        branchNodes.add(level3Node);
      }
    }

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.amber.shade50.withValues(alpha: 51), // 0.2 * 255 = ~51
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Part label
          Container(
            margin: EdgeInsets.only(right: 8),
            child: Text(
              'Part ${parallelChild['sequence'] ?? 1}.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.amber.shade800,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // First node (Inventory Manager or Finance Officer)
          Text(
            branchNodes[0]['text'] ?? '',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          // Spacer
          SizedBox(width: 16),

          // Second node (System Completes Purchase Order)
          if (branchNodes.length >= 2)
            Text(
              branchNodes[1]['text'] ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),

          // Spacer
          if (branchNodes.length >= 2) SizedBox(width: 16),

          // Third node (Vendor Receives Purchase Order or Delivery Receives Pickup Order)
          if (branchNodes.length >= 3)
            Text(
              branchNodes[2]['text'] ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
        ],
      ),
    );
  }

  // Build a workflow tree node and its children
  Widget _buildWorkflowTreeNode(
      Map<String, dynamic> node, List<dynamic> allNodes, int indentLevel) {
    final bool hasChildren = node.containsKey('children') &&
        node['children'] != null &&
        (node['children'] as List).isNotEmpty;
    final String nodeText = node['text'] ?? '';
    final bool isRejection = node['isRejection'] ?? false;
    final bool isParallel = node['isParallel'] ?? false;
    final bool hasCheckmark = node['hasCheckmark'] ?? false;
    final bool hasX = node['hasX'] ?? false;
    final String altText = node['altText'] ?? '';

    // Check if this is an Alt path node
    final bool isAltPath = altText.isNotEmpty;

    return Stack(
      children: [
        // Vertical connector line for children
        if (hasChildren)
          Positioned(
            left: indentLevel * 24.0 + 8,
            top: 24,
            bottom: 0,
            width: 1,
            child: Container(
              color: Colors.grey.shade300,
            ),
          ),

        // Vertical connector line for siblings
        if (indentLevel > 0)
          Positioned(
            left: (indentLevel - 1) * 24.0 + 8,
            top: 0,
            bottom: 0,
            width: 1,
            child: Container(
              color: Colors.grey.shade300,
            ),
          ),

        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current node
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Indentation space with Alt text if applicable
                if (isAltPath)
                  SizedBox(
                    width: indentLevel * 24.0,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 4),
                        child: Text(
                          'Alt. $altText',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),
                  )
                else
                  SizedBox(width: indentLevel * 24.0),

                // Circle indicator
                Container(
                  margin: EdgeInsets.only(top: 2, right: 8),
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade400, width: 0.5),
                    color: Colors.white,
                  ),
                ),

                // Node content
                if (nodeText.isNotEmpty)
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            nodeText,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: isRejection
                                  ? Colors.red.shade700
                                  : isParallel
                                      ? Colors.amber.shade800
                                      : Colors.black,
                            ),
                          ),
                        ),

                        // Link icon for Procurement Officer step
                        if (nodeText.contains(
                            'Procurement Officer Creates Purchase Order'))
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            child: Icon(
                              Icons.link,
                              color: Colors.green.shade700,
                              size: 20,
                            ),
                          ),

                        // Checkmark icon
                        if (hasCheckmark)
                          Container(
                            width: 20,
                            height: 20,
                            margin: EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.green.shade50,
                              border: Border.all(color: Colors.green.shade400),
                            ),
                            child: Icon(
                              Icons.check,
                              size: 14,
                              color: Colors.green.shade700,
                            ),
                          ),

                        // X icon for rejection
                        if (hasX ||
                            isRejection ||
                            nodeText.contains('Rejection'))
                          Container(
                            width: 20,
                            height: 20,
                            margin: EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.red.shade50,
                              border: Border.all(color: Colors.red.shade400),
                            ),
                            child: Icon(
                              Icons.close,
                              size: 14,
                              color: Colors.red.shade700,
                            ),
                          ),

                        // Parallel label
                        if (isParallel)
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            padding: EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber.shade50,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.amber.shade200),
                            ),
                            child: Text(
                              'Part.',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.amber.shade800,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),

            // Children nodes
            if (hasChildren)
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var child in (node['children'] as List<dynamic>))
                      _buildWorkflowTreeNode(child as Map<String, dynamic>,
                          allNodes, indentLevel + 1),
                  ],
                ),
              ),

            // Add spacing between top-level nodes
            if (indentLevel == 0) SizedBox(height: 24),
          ],
        ),

        // Horizontal connector line
        if (indentLevel > 0)
          Positioned(
            left: (indentLevel - 1) * 24.0 + 16,
            top: 10,
            child: Container(
              width: 16,
              height: 1,
              color: Colors.grey.shade300,
            ),
          ),
      ],
    );
  }

  // Helper method to get section abbreviation
  String _getSectionAbbreviation(String sectionName) {
    switch (sectionName) {
      case 'Attributes':
        return 'ATT';
      case 'Business Rules':
        return 'BR';
      case 'Relationships':
        return 'REL';
      case 'Circuit Board':
        return 'CB';
      case 'Constants & Validations':
        return 'CV';
      default:
        return sectionName.substring(0, 1);
    }
  }

  // Helper method to build field with tooltip
  Widget _buildFieldWithTooltip(String fieldName) {
    return _buildNewFieldTooltip(fieldName);
  }

  // New tooltip design matching the reference image
  Widget _buildNewFieldTooltip(String fieldName) {
    // Field-specific content
    List<Map<String, String>> tooltipItems = [];

    // Configure content based on field name
    switch (fieldName) {
      case 'Customer ID':
        tooltipItems = [
          {
            'text':
                'On Delete: Cascade (Delete Orders when Customer is deleted)',
            'prefix': '• '
          },
          {
            'text':
                'On Update: Cascade (Update details in Order when detail in Customer is updated)',
            'prefix': '• '
          },
        ];
        break;
      case 'Name':
        tooltipItems = [
          {
            'text': 'On Delete: Restrict (Cannot delete if referenced)',
            'prefix': '• '
          },
          {
            'text': 'On Update: Cascade (Update related records)',
            'prefix': '• '
          },
        ];
        break;
      case 'Email':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      case 'Phone Number':
        tooltipItems = [
          {'text': 'On Delete: Set Null', 'prefix': '• '},
          {'text': 'On Update: Cascade', 'prefix': '• '},
        ];
        break;
      case 'Address':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      case 'Gender':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      default:
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        richMessage: WidgetSpan(
          child: Container(
            width: 350,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 51), // 0.2 * 255 = ~51
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Blue header with icon
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade700,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Left side - Icon with white background
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.list_alt,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 12),
                      // Right side - ID and version info
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                '• ID: at001',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                '• Version: V00012',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                '• Created: 12/4/2025 by john smith',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                '• Last Modified: 15/5/2025 by jan dae',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Editable Properties section
                Container(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Editable Properties:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(height: 8),
                      ...tooltipItems.map((item) => Padding(
                            padding: EdgeInsets.only(bottom: 4),
                            child: Text(
                              '${item['prefix'] ?? ''}${item['text'] ?? ''}',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.black87,
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        preferBelow: false,
        verticalOffset: 20,
        padding: EdgeInsets.zero,
        margin: EdgeInsets.zero,
        showDuration: Duration(seconds: 10),
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [],
        ),
        child: Text(
          fieldName,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'TiemposText',
            fontSize: 14,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  // Toggle recording (start/stop)
  Future<void> _toggleRecording() async {
    await _multimediaService.toggleRecording(context);
  }

  // Handle sending a message
  void _sendMessage() async {
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);
    if (provider.isProjectCreated) {
      // provider.addMessage(ChatMessage(
      //   content: chatController.text.trim(),
      //   isUser: true,
      //   // fileData: fileData,
      // ));
      // // _scrollToBottom();
      // provider.addMessage(ChatMessage(
      //     content:
      //         "1. __Dynamic Mode Loading__: The system now fetches available modes from the API endpoint and displays them as quick message options\n2. __Follow-up Suggestions__: When the API returns follow-up suggestions, they are displayed as clickable buttons below the response\n3. __Seamless Integration__: Clicking a follow-up suggestion automatically sends it as a new message\n4. __Backward Compatibility__: The existing Solution quick message is preserved and added at the end\n5. __Error Handling__: Proper error handling for API calls and fallback mechanisms\n##",
      //     isUser: false,
      //     followUpSuggestions: [
      //       "1. __Dynamic Mode Loading__: The system now fetches a",
      //       "2. __Dynamic Mode Loading__: The system now fetches a",
      //       "3. __Dynamic Mode Loading__: The system now fetches a"
      //     ]
      //     // fileData: fileData,
      //     ));
      // return;

      String text = chatController.text.trim();
      if (text.isEmpty) {
        if (fileUploadOcrResponse != null &&
            fileUploadOcrResponse?.data?.originalText != null) {
          text = fileUploadOcrResponse!.data!.originalText!;
        } else {
          return;
        }
      }

      // Prepare message content
      String messageContent = text.replaceFirst(text[0], text[0].toUpperCase());

      // If a file is uploaded, include the file information in the message
      FileUploadOcrResponse? fileData;
      String finalMessageContent = messageContent;

      if (isFileUploaded) {
        fileData = fileUploadOcrResponse;
        isOcrExist = true;
        // {
        //   'fileName': uploadedFileName,
        //   'extractedText': uploadedFileText,
        // };

        // Concatenate the extracted text with the user input for the API call
        // But keep the original message content for display
        if (uploadedFileText.isNotEmpty) {
          Logger.info(
              'Concatenating extracted text with user input for API call');
          Logger.info('Original message: $messageContent');
          Logger.info('Extracted text: $uploadedFileText');

          // Only concatenate if the user hasn't already included the OCR text
          if (!messageContent.contains(uploadedFileText)) {
            finalMessageContent = "$uploadedFileText\n\n$messageContent";
            Logger.info('Final message for API: $finalMessageContent');
          }
        }
        // Reset file upload state after sending
        setState(() {
          isFileUploading = false; // Clear upload process state
          isFileUploaded = false; // Clear completed upload state
          isFileProcessing = false; // Clear processing state
        });
      }

      setState(() {
        // Add user message to the UI (display only the original message)
        provider.addMessage(ChatMessage(
          content: messageContent,
          isUser: true,
          // fileData: fileData,
        ));

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });

        // Clear input field
        chatController.text = '';

        // Show loading indicator
        isLoading = true;
      });

      // Store the final message content (with OCR text if available) for API calls
      provider.lastUserMessageForApi = finalMessageContent;

      // Check which quick message is selected
      if (selectedQuickMessage == 'General') {
        // Call the general API
        await provider.sendGeneralQuestion(text).then((response) async {
          await _processApiResponseWithTranslation(response, 'General');

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }).catchError((error) {
          setState(() {
            isLoading = false;
            provider.addMessage(ChatMessage(
              content: 'Error: $error',
              isUser: false,
            ));
          });

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        });
      } else if (selectedQuickMessage == 'Internet') {
        // Call the internet API with user ID from AuthService
        await provider.sendInternetQuestion(text).then((response) async {
          await _processApiResponseWithTranslation(response, 'Internet');

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }).catchError((error) {
          setState(() {
            isLoading = false;
            provider.addMessage(ChatMessage(
              content: 'Error: $error',
              isUser: false,
            ));
          });

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        });
      } else if (selectedQuickMessage == 'NSL') {
        // Call the NSL API with user ID from AuthService
        await provider.sendNslQuestion(text).then((response) async {
          await _processApiResponseWithTranslation(response, 'NSL');

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }).catchError((error) {
          setState(() {
            isLoading = false;
            provider.addMessage(ChatMessage(
              content: 'Error: $error',
              isUser: false,
            ));
          });

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        });
      } else if (selectedQuickMessage == "Solution") {
        await _handleConversation(text);

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      } else if (selectedQuickMessage == "Manual") {
        await _handleConversation(text);

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      } else {
        // Check if this is a mode-based message (not "Solution")
        final provider =
            Provider.of<WebHomeProviderStatic>(context, listen: false);
        final modeId =
            provider.getModeIdByName(provider.selectedQuickMessage ?? '');

        if (modeId != null) {
          // This is a mode-based message, use the new mode API
          await provider.sendModeQuestion(text).then((response) async {
            setState(() {
              isLoading = false;

              if (response['success']) {
                // Extract the response content
                final responseText =
                    response['data']['response'] ?? 'No response provided';

                // Extract follow-up suggestions if available
                List<String>? followUpSuggestions;
                if (response['data']['follow_up_suggestions'] != null) {
                  final suggestions = response['data']['follow_up_suggestions'];
                  if (suggestions is List && suggestions.isNotEmpty) {
                    followUpSuggestions = suggestions.cast<String>();
                  }
                }

                provider.addMessage(ChatMessage(
                  content: responseText,
                  isUser: false,
                  followUpSuggestions: followUpSuggestions,
                ));
              } else {
                // Add error message
                provider.addMessage(ChatMessage(
                  content: 'Error: ${response['message']}',
                  isUser: false,
                ));
              }
            });

            // Scroll to bottom after the UI updates
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToBottom();
            });
          }).catchError((error) {
            setState(() {
              isLoading = false;
              provider.addMessage(ChatMessage(
                content: 'Error: $error',
                isUser: false,
              ));
            });

            // Scroll to bottom after the UI updates
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToBottom();
            });
          });
        } else {
          // Fallback to existing conversation flow
          await _handleConversation(text);

          // Scroll to bottom after the UI updates
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }
      }
    } else {
      provider.addMessage(ChatMessage(
        content: '',
        isUser: false,
      ));
      return;
    }
  }

  Widget chatField(BuildContext context,
      {double? width, double? height, bool dropdownAbove = false}) {
    return ChatField(
      width: width,
      height: height,
      dropdownAbove: dropdownAbove,
      isGeneralLoading: isLoading,
      isFileLoading:
          isFileUploading, // Use active file uploading state for loading indicator
      isSpeechLoading: isAudioLoading, // Use audio loading state
      parentState: this,
      onSendMessage: _sendMessage,
      onCancelRequest: _cancelRequest,
      onFileSelected: (fileName, filePath) {
        _handleFileSelection(fileName, filePath);
      },
      onToggleRecording: _toggleRecording,
      controller: chatController,
      multimediaService: _multimediaService,
      initialUploadedFileName: uploadedFileName,
      initialUploadedFileText: uploadedFileText,
      initialIsFileUploaded: isFileUploaded,
      initialIsFileProcessing: isFileProcessing,
      onFileUploadTap: toggleOcrPanel,
      onFileCloseTap: closeOcrPanel,
      extraWidgets: messages.isEmpty
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<WebHomeProviderStatic>(
                  builder: (context, provider, child) {
                    // Show modes from API if available, otherwise show default Solution
                    final modes = provider.availableModes;

                    return Wrap(
                      alignment: WrapAlignment.start,
                      spacing: AppSpacing.xs,
                      runSpacing: AppSpacing.xs,
                      children: [
                        if (modes.isNotEmpty)
                          // Show modes from API
                          ...modes.map((mode) => quickMessage(
                                text: mode.name,
                                modeId: mode.id,
                              )),

                        // Always add the Solution message at the end
                        // quickMessage(
                        //     text: AppLocalizations.of(context)
                        //         .translate('home.solution')),
                      ],
                    );
                  },
                ),
              ],
            )
          : null, // Don't show quick messages when conversation has started
    );
  }

  // Get image path for quick message type
  String getQuickMessageImagePath(String text) {
    if (text.toLowerCase().contains("nsl")) {
      return 'assets/images/book_nsl.svg';
    } else if (text.toLowerCase().contains("business")) {
      return 'assets/images/loader_general.svg';
    } else {
      return 'assets/images/bulb_solution.svg';
    }
    // switch (text) {
    //   case 'NSL':
    //     return 'assets/images/book_nsl.svg';
    //   case 'Solution':
    //   case 'Discovery':
    //     return 'assets/images/bulb_solution.svg';
    //   case 'General':
    //     return 'assets/images/loader_general.svg';
    //   case 'Internet':
    //     return 'assets/images/internet.svg';
    //   default:
    //     return 'assets/images/quickmessage/chat_icon.png';
    // }
  }

  Widget quickMessage({required String text, String? modeId}) {
    // Get the provider
    final provider = Provider.of<WebHomeProviderStatic>(context, listen: false);

    // Create a custom stateful widget for hover effect with selection state
    return QuickMessageButton(
      text: text,
      imagePath: getQuickMessageImagePath(text),
      isSelected: provider.selectedQuickMessage == text,
      onTap: () {
        // Toggle selection if already selected, otherwise select this message
        if (text == "Discovery") {
          provider.currentScreenIndex = ScreenConstants.discovery;
        } else {
          provider.selectedQuickMessage =
              provider.selectedQuickMessage == text ? null : text;

          // Store the mode ID if provided
          if (modeId != null && provider.selectedQuickMessage != null) {
            provider.selectedModeId = modeId;
          }

          if (provider.selectedQuickMessage != null) {
            chatController.text = ''; // Clear any existing text
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Row(
          children: [
            // Main content area (resized when side panel is shown)
            Expanded(
              flex: 3, // Give it a larger flex value than the OCR panel
              child: Column(
                children: [
                  // Add the BookSolutionAppBar at the top of main content (only when showBookSolutionAppBar is true)
                  if (provider.showBookSolutionAppBar)
                    BookSolutionAppBar(
                      projectName: 'Project Name',
                      showChatToggle:
                          _isAIEnabled, // Only show chat toggle when AI is enabled
                      showAIToggle: true,
                      isChatEnabled: _isChatEnabled,
                      isAIEnabled: _isAIEnabled,
                      onChatToggle: () {
                        setState(() {
                          _isChatEnabled = !_isChatEnabled;
                        });
                      },
                      onAIToggle: () {
                        final provider = Provider.of<WebHomeProviderStatic>(
                            context,
                            listen: false);
                        setState(() {
                          _isAIEnabled = !_isAIEnabled;
                          // When AI is enabled, show Discovery tab in solution details panel
                          // When AI is disabled (Manual), show Development tab in solution details panel
                          isDiscoverySelected = _isAIEnabled;

                          // Close the globalLibrary panel when toggling from Manual to AI mode
                          if (_isAIEnabled && provider.showGlobalLibrary) {
                            provider.showGlobalLibrary = false;
                          }
                        });
                      },
                    ),
                  // Main content below AppBar
                  Expanded(
                    child: _isAIEnabled
                        ? NSLKnowledgeLoaderWrapper(
                            isLoading: isAudioLoading,
                            child: Column(
                              children: [
                                // Main content area (resized when side panel is shown)
                                Expanded(
                                  flex:
                                      3, // Give it a larger flex value than the OCR panel
                                  child: NSLKnowledgeLoaderWrapper(
                                    isLoading: isAudioLoading,
                                    child: Column(
                                      children: [
                                        // Different layouts based on whether chat has started
                                        if (messages.isEmpty)
                                          Expanded(
                                            child: Row(
                                              children: [
                                                chatHistory(),
                                                Expanded(
                                                  child: SingleChildScrollView(
                                                    child: Column(
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Provider.of<WebHomeProviderStatic>(
                                                                        context)
                                                                    .isChatHistoryExpanded
                                                                ? Container(
                                                                    width: 150,
                                                                  )
                                                                : MediaQuery.of(context)
                                                                            .size
                                                                            .width >
                                                                        1200
                                                                    ? Expanded(
                                                                        child:
                                                                            SizedBox())
                                                                    : SizedBox(
                                                                        width: MediaQuery.of(context).size.width *
                                                                            0.1),
                                                            Expanded(
                                                              flex: 3,
                                                              child: Column(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .center,
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  // SizedBox(height: AppSpacing.md),
                                                                  // SizedBox(height: 89),
                                                                  // SizedBox(
                                                                  //   height: MediaQuery.of(context).size.height * 0.36, // 10% of screen height
                                                                  // ),
                                                                  Center(
                                                                    child: Consumer<
                                                                        AuthProvider>(
                                                                      builder: (context,
                                                                          authProvider,
                                                                          _) {
                                                                        // Get the username from the user profile
                                                                        final String
                                                                            firstName =
                                                                            authProvider.user?.firstName != null
                                                                                ? (authProvider.user?.firstName ?? "").capitalize() ?? ''
                                                                                : "";

                                                                        // Get the localized greeting text
                                                                        final String greeting = AppLocalizations.of(context).translate('home.greeting').replaceAll(
                                                                            '{name}',
                                                                            firstName.isNotEmpty
                                                                                ? firstName
                                                                                : 'Sam');

                                                                        return Text(
                                                                          greeting,
                                                                          style: TextStyle(
                                                                              fontSize: 34,
                                                                              fontFamily: 'TiemposText'),
                                                                        );
                                                                      },
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                      height:
                                                                          12),
                                                                  // Chat field in the center
                                                                  chatField(
                                                                      context,
                                                                      height:
                                                                          148),
                                                                  // Solution buttons below chat field
                                                                  // if (messages.isEmpty) ...[
                                                                  //   SizedBox(height: 3),
                                                                  //   Row(
                                                                  //     mainAxisAlignment:
                                                                  //         MainAxisAlignment.start,
                                                                  //     children: [
                                                                  // // AI Generated Solution button
                                                                  // MouseRegion(
                                                                  //   onEnter: (_) => setState(() =>
                                                                  //       isSolutionHovered = true),
                                                                  //   onExit: (_) => setState(() =>
                                                                  //       isSolutionHovered =
                                                                  //           false),
                                                                  //   cursor:
                                                                  //       SystemMouseCursors.click,
                                                                  //   child: GestureDetector(
                                                                  //     onTap: () {
                                                                  //       Logger.info(
                                                                  //           'AI Generated Solution selected');
                                                                  //       // Set the selected quick message to Solution to enable the same functionality
                                                                  //       final provider = Provider
                                                                  //           .of<WebHomeProviderStatic>(
                                                                  //               context,
                                                                  //               listen: false);
                                                                  //       provider.selectedQuickMessage =
                                                                  //           'Solution';
                                                                  //     },
                                                                  //     child: Container(
                                                                  //       padding:
                                                                  //           EdgeInsets.symmetric(
                                                                  //               horizontal: 16,
                                                                  //               vertical: 8),
                                                                  //       decoration: BoxDecoration(
                                                                  //         color: Colors.white,
                                                                  //         border: Border.all(
                                                                  //             color: Provider.of<WebHomeProviderStatic>(
                                                                  //                             context)
                                                                  //                         .selectedQuickMessage ==
                                                                  //                     'Solution'
                                                                  //                 ? Color(
                                                                  //                     0xff0058FF) // Blue color for selected state
                                                                  //                 : isSolutionHovered
                                                                  //                     ? Color(
                                                                  //                         0xff0058FF)
                                                                  //                     : Color(
                                                                  //                         0xffD0D0D0),
                                                                  //             width: 1),
                                                                  //         borderRadius:
                                                                  //             BorderRadius
                                                                  //                 .circular(4),
                                                                  //       ),
                                                                  //       child: Row(
                                                                  //         mainAxisSize:
                                                                  //             MainAxisSize.min,
                                                                  //         children: [
                                                                  //           SvgPicture.asset(
                                                                  //             'assets/images/sparkle-stars-ai.svg',
                                                                  //           ),
                                                                  //           // Icon(
                                                                  //           //     Icons
                                                                  //           //         .auto_awesome,
                                                                  //           //     size: 16,
                                                                  //           //     color: Colors.grey
                                                                  //           //         .shade600),
                                                                  //           SizedBox(width: 8),
                                                                  //           Text(
                                                                  //             'AI Generated Solution',
                                                                  //             style: TextStyle(
                                                                  //               fontSize: 14,
                                                                  //               fontWeight:
                                                                  //                   FontWeight
                                                                  //                       .w400,
                                                                  //               fontFamily:
                                                                  //                   'TiemposText',
                                                                  //               color:
                                                                  //                   Colors.black,
                                                                  //             ),
                                                                  //           ),
                                                                  //         ],
                                                                  //       ),
                                                                  //     ),
                                                                  //   ),
                                                                  // ),
                                                                  // SizedBox(width: 12),
                                                                  // Create Solution Manually button
                                                                  // MouseRegion(
                                                                  //   onEnter: (_) =>
                                                                  //       setState(() =>
                                                                  //           isManualHovered =
                                                                  //               true),
                                                                  //   onExit: (_) =>
                                                                  //       setState(() =>
                                                                  //           isManualHovered =
                                                                  //               false),
                                                                  //   cursor:
                                                                  //       SystemMouseCursors
                                                                  //           .click,
                                                                  //   child:
                                                                  //       GestureDetector(
                                                                  //     onTap:
                                                                  //         () {
                                                                  //       Logger.info(
                                                                  //           'Create Solution Manually selected - navigating to new screen');
                                                                  //       Provider.of<WebHomeProvider>(context, listen: false).currentScreenIndex =
                                                                  //           ScreenConstants.manualCreationStaticScreen;
                                                                  //     },
                                                                  //     child:
                                                                  //         Container(
                                                                  //       padding: EdgeInsets.symmetric(
                                                                  //           horizontal:
                                                                  //               16,
                                                                  //           vertical:
                                                                  //               8),
                                                                  //       decoration:
                                                                  //           BoxDecoration(
                                                                  //         color:
                                                                  //             Colors.white,
                                                                  //         border: Border.all(
                                                                  //             color: Provider.of<WebHomeProviderStatic>(context).selectedQuickMessage == "Manual"
                                                                  //                 ? Color(0xff0058FF) // Blue color for selected state
                                                                  //                 : isManualHovered
                                                                  //                     ? Color(0xff0058FF)
                                                                  //                     : Color(0xffD0D0D0),
                                                                  //             width: 1),
                                                                  //         borderRadius:
                                                                  //             BorderRadius.circular(4),
                                                                  //       ),
                                                                  //       child:
                                                                  //           Row(
                                                                  //         mainAxisSize:
                                                                  //             MainAxisSize.min,
                                                                  //         children: [
                                                                  //           SvgPicture.asset(
                                                                  //             'assets/images/file-text.svg',
                                                                  //           ),
                                                                  //           SizedBox(width: 8),
                                                                  //           Text(
                                                                  //             'Create Solution Manually',
                                                                  //             style: TextStyle(
                                                                  //               fontSize: 14,
                                                                  //               fontFamily: 'TiemposText',
                                                                  //               fontWeight: FontWeight.w400,
                                                                  //               color: Colors.black,
                                                                  //             ),
                                                                  //           ),
                                                                  //         ],
                                                                  //       ),
                                                                  //     ),
                                                                  //   ),
                                                                  // ),
                                                                  //     ],
                                                                  //   ),
                                                                  // ],
                                                                  // SizedBox(height: AppSpacing.xxs),
                                                                  // Quick message suggestions
                                                                  // SizedBox(height: 20),
                                                                  // Container(
                                                                  //   padding: EdgeInsets.only(
                                                                  //       left: 0, right: 40),
                                                                  //   child: Column(children: [
                                                                  //     Row(
                                                                  //       mainAxisAlignment:
                                                                  //           MainAxisAlignment
                                                                  //               .spaceBetween,
                                                                  //       crossAxisAlignment:
                                                                  //           CrossAxisAlignment.start,
                                                                  //       children: [
                                                                  //         Row(
                                                                  //           children: [
                                                                  //             Visibility(
                                                                  //               visible:
                                                                  //                   selectedColumnItem !=
                                                                  //                       null,
                                                                  //               maintainSize: true,
                                                                  //               maintainAnimation: true,
                                                                  //               maintainState: true,
                                                                  //               child: MouseRegion(
                                                                  //                 cursor:
                                                                  //                     SystemMouseCursors
                                                                  //                         .click,
                                                                  //                 child:
                                                                  //                     GestureDetector(
                                                                  //                   onTap: () {
                                                                  //                     // Deselect whichever column is currently selected
                                                                  //                     if (selectedColumnItem ==
                                                                  //                         'Inspirations') {
                                                                  //                       _handleInspirationsClick();
                                                                  //                     } else if (selectedColumnItem ==
                                                                  //                         'My Chats') {
                                                                  //                       _handleChatClick();
                                                                  //                     } else if (selectedColumnItem ==
                                                                  //                         'Solutions') {
                                                                  //                       _handleSolutionsClick();
                                                                  //                     }
                                                                  //                   },
                                                                  //                   child: Container(
                                                                  //                     margin: EdgeInsets
                                                                  //                         .only(
                                                                  //                             right:
                                                                  //                                 18),
                                                                  //                     child: const Icon(
                                                                  //                         Icons
                                                                  //                             .arrow_back,
                                                                  //                         color: Colors
                                                                  //                             .grey,
                                                                  //                         size: 18),
                                                                  //                   ),
                                                                  //                 ),
                                                                  //               ),
                                                                  //             ),
                                                                  //             buildColumnItem(
                                                                  //                 "assets/images/inspirations.svg",
                                                                  //                 "Inspirations",
                                                                  //                 onTap:
                                                                  //                     _handleInspirationsClick,
                                                                  //                 isSelected:
                                                                  //                     selectedColumnItem ==
                                                                  //                         'Inspirations'),
                                                                  //           ],
                                                                  //         ),
                                                                  //         buildColumnItem(
                                                                  //             "assets/images/my_chats.svg",
                                                                  //             "My Chats",
                                                                  //             onTap: _handleChatClick,
                                                                  //             isSelected:
                                                                  //                 selectedColumnItem ==
                                                                  //                     'My Chats'),
                                                                  //         buildColumnItem(
                                                                  //             "assets/images/solutions.svg",
                                                                  //             "Solutions",
                                                                  //             onTap:
                                                                  //                 _handleSolutionsClick,
                                                                  //             isSelected:
                                                                  //                 selectedColumnItem ==
                                                                  //                     'Solutions'),
                                                                  //       ],
                                                                  //     )
                                                                  //   ]),
                                                                  // ),

                                                                  // Column(
                                                                  //   crossAxisAlignment:
                                                                  //       CrossAxisAlignment.center,
                                                                  //   children: [
                                                                  //     Row(
                                                                  //       mainAxisAlignment:
                                                                  //           MainAxisAlignment
                                                                  //               .spaceBetween,
                                                                  //       crossAxisAlignment:
                                                                  //           CrossAxisAlignment.start,
                                                                  //       children: [
                                                                  //         // First column with left-to-right animation
                                                                  //         SlideTransition(
                                                                  //           position:
                                                                  //               _firstColumnAnimation,
                                                                  //           child: Column(
                                                                  //             children: [
                                                                  //               // Show back arrow and inspirations item in a row when selected
                                                                  //               if (showEducationIcons)
                                                                  //                 SizedBox(height: 10),
                                                                  //               // First column - Educational icons
                                                                  //               if (showEducationIcons)
                                                                  //                 MouseRegion(
                                                                  //                   cursor:
                                                                  //                       SystemMouseCursors
                                                                  //                           .click,
                                                                  //                   onEnter: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isFirstColumnHovered =
                                                                  //                               true),
                                                                  //                   onExit: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isFirstColumnHovered =
                                                                  //                               false),
                                                                  //                   child:
                                                                  //                       GestureDetector(
                                                                  //                     onTap:
                                                                  //                         _handleInspirationsClick,
                                                                  //                     child: Container(
                                                                  //                       width: 212,
                                                                  //                       height: 284,
                                                                  //                       // padding: EdgeInsets.all(12),
                                                                  //                       decoration:
                                                                  //                           BoxDecoration(
                                                                  //                         // color: Colors.white,
                                                                  //                         borderRadius:
                                                                  //                             BorderRadius
                                                                  //                                 .circular(
                                                                  //                                     12),
                                                                  //                       ),
                                                                  //                       child:
                                                                  //                           Container(
                                                                  //                         decoration:
                                                                  //                             BoxDecoration(
                                                                  //                           borderRadius:
                                                                  //                               BorderRadius.circular(
                                                                  //                                   12),
                                                                  //                         ),
                                                                  //                         child: Stack(
                                                                  //                           children: [
                                                                  //                             // Background SVG image positioned in bottom layer with stack animation
                                                                  //                             Positioned
                                                                  //                                 .fill(
                                                                  //                               child:
                                                                  //                                   AnimatedBuilder(
                                                                  //                                 animation:
                                                                  //                                     _firstBgController,
                                                                  //                                 builder:
                                                                  //                                     (context, child) {
                                                                  //                                   return Transform.scale(
                                                                  //                                     scale: _firstBgScaleAnimation.value,
                                                                  //                                     child: FadeTransition(
                                                                  //                                       opacity: _firstBgAnimation,
                                                                  //                                       child: AnimatedContainer(
                                                                  //                                         duration: Duration(milliseconds: 600),
                                                                  //                                         curve: Curves.easeInOut,
                                                                  //                                         transform: Matrix4.translationValues(
                                                                  //                                           (1 - _firstBgAnimation.value) * 20 + (isFirstColumnHovered ? 8 : 0),
                                                                  //                                           (1 - _firstBgAnimation.value) * 20,
                                                                  //                                           0,
                                                                  //                                         ),
                                                                  //                                         child: ClipRRect(
                                                                  //                                           borderRadius: BorderRadius.circular(12),
                                                                  //                                           child: SvgPicture.asset(
                                                                  //                                             'assets/images/chat-session-shape-bg.svg',
                                                                  //                                             fit: BoxFit.cover,
                                                                  //                                             alignment: Alignment.bottomRight,
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                       ),
                                                                  //                                     ),
                                                                  //                                   );
                                                                  //                                 },
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                             // Main content layer with fade animation
                                                                  //                             FadeTransition(
                                                                  //                               opacity:
                                                                  //                                   _firstContentAnimation,
                                                                  //                               child:
                                                                  //                                   Column(
                                                                  //                                 children: [
                                                                  //                                   isImagesLoading
                                                                  //                                       ? Center(child: CircularProgressIndicator())
                                                                  //                                       : Expanded(
                                                                  //                                           child: Container(
                                                                  //                                             width: 177,
                                                                  //                                             height: 261,
                                                                  //                                             padding: EdgeInsets.all(0),
                                                                  //                                             child: Image.asset(
                                                                  //                                               imageData.containsKey('education_icons') && imageData['education_icons'].length > 0 ? imageData['education_icons'][0] : 'assets/images/education_icon1_new.png',
                                                                  //                                               fit: BoxFit.contain, // Changed to contain to preserve aspect ratio
                                                                  //                                             ),
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                 ],
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                           ],
                                                                  //                         ),
                                                                  //                       ),
                                                                  //                     ),
                                                                  //                   ),
                                                                  //                 ),
                                                                  //             ],
                                                                  //           ),
                                                                  //         ),

                                                                  //         // Second column with top-to-bottom animation
                                                                  //         SlideTransition(
                                                                  //           position:
                                                                  //               _secondColumnAnimation,
                                                                  //           child: Column(
                                                                  //             children: [
                                                                  //               if (showChatSessions)
                                                                  //                 SizedBox(height: 10),
                                                                  //               if (showChatSessions)
                                                                  //                 MouseRegion(
                                                                  //                   cursor:
                                                                  //                       SystemMouseCursors
                                                                  //                           .click,
                                                                  //                   onEnter: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isSecondColumnHovered =
                                                                  //                               true),
                                                                  //                   onExit: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isSecondColumnHovered =
                                                                  //                               false),
                                                                  //                   child:
                                                                  //                       GestureDetector(
                                                                  //                     onTap:
                                                                  //                         _handleChatClick,
                                                                  //                     child: Container(
                                                                  //                       width: 212,
                                                                  //                       height: 284,
                                                                  //                       // padding: EdgeInsets.all(12),
                                                                  //                       decoration:
                                                                  //                           BoxDecoration(
                                                                  //                         // color: Colors.white,
                                                                  //                         borderRadius:
                                                                  //                             BorderRadius
                                                                  //                                 .circular(
                                                                  //                                     12),
                                                                  //                       ),

                                                                  //                       child:
                                                                  //                           Container(
                                                                  //                         decoration:
                                                                  //                             BoxDecoration(
                                                                  //                           borderRadius:
                                                                  //                               BorderRadius.circular(
                                                                  //                                   12),
                                                                  //                         ),
                                                                  //                         child: Stack(
                                                                  //                           children: [
                                                                  //                             // Background SVG image positioned in bottom layer with stack animation
                                                                  //                             Positioned
                                                                  //                                 .fill(
                                                                  //                               child:
                                                                  //                                   AnimatedBuilder(
                                                                  //                                 animation:
                                                                  //                                     _secondBgController,
                                                                  //                                 builder:
                                                                  //                                     (context, child) {
                                                                  //                                   return Transform.scale(
                                                                  //                                     scale: _secondBgScaleAnimation.value,
                                                                  //                                     child: FadeTransition(
                                                                  //                                       opacity: _secondBgAnimation,
                                                                  //                                       child: AnimatedContainer(
                                                                  //                                         duration: Duration(milliseconds: 600),
                                                                  //                                         curve: Curves.easeInOut,
                                                                  //                                         transform: Matrix4.translationValues(
                                                                  //                                           (1 - _secondBgAnimation.value) * 15 + (isSecondColumnHovered ? 8 : 0),
                                                                  //                                           (1 - _secondBgAnimation.value) * -25,
                                                                  //                                           0,
                                                                  //                                         ),
                                                                  //                                         child: ClipRRect(
                                                                  //                                           borderRadius: BorderRadius.circular(12),
                                                                  //                                           child: SvgPicture.asset(
                                                                  //                                             'assets/images/chat-session-shape-bg.svg',
                                                                  //                                             fit: BoxFit.cover,
                                                                  //                                             alignment: Alignment.bottomRight,
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                       ),
                                                                  //                                     ),
                                                                  //                                   );
                                                                  //                                 },
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                             // Main content layer with fade animation
                                                                  //                             FadeTransition(
                                                                  //                               opacity:
                                                                  //                                   _secondContentAnimation,
                                                                  //                               child:
                                                                  //                                   Column(
                                                                  //                                 mainAxisAlignment:
                                                                  //                                     MainAxisAlignment.center,
                                                                  //                                 children: [
                                                                  //                                   Container(
                                                                  //                                     width: 177,
                                                                  //                                     height: 261,
                                                                  //                                     padding: EdgeInsets.all(12),
                                                                  //                                     decoration: BoxDecoration(
                                                                  //                                       color: Color(0xFFF2F6F8),
                                                                  //                                       borderRadius: BorderRadius.circular(12),
                                                                  //                                       border: Border.all(
                                                                  //                                         color: Color(0xFFDBE9F2), // Hex #DBE9F2
                                                                  //                                         width: 1, // 1px border
                                                                  //                                       ),
                                                                  //                                     ),
                                                                  //                                     child: Column(
                                                                  //                                       children: [
                                                                  //                                         // First chat session
                                                                  //                                         Container(
                                                                  //                                           padding: EdgeInsets.all(12),
                                                                  //                                           decoration: BoxDecoration(
                                                                  //                                             color: Colors.white,
                                                                  //                                             borderRadius: BorderRadius.circular(8),
                                                                  //                                             border: Border.all(color: Colors.grey.shade200),
                                                                  //                                           ),
                                                                  //                                           child: Column(
                                                                  //                                             crossAxisAlignment: CrossAxisAlignment.start,
                                                                  //                                             children: [
                                                                  //                                               Text(
                                                                  //                                                 "Chat session name To Store",
                                                                  //                                                 style: TextStyle(
                                                                  //                                                   fontSize: 14,
                                                                  //                                                   fontWeight: FontWeight.w500,
                                                                  //                                                   color: Color(0xff848484),
                                                                  //                                                 ),
                                                                  //                                               ),
                                                                  //                                               SizedBox(height: 18),
                                                                  //                                               Align(
                                                                  //                                                 alignment: Alignment.centerRight,
                                                                  //                                                 child: Text(
                                                                  //                                                   "01/05/2023",
                                                                  //                                                   style: TextStyle(
                                                                  //                                                     fontSize: 10,
                                                                  //                                                     color: Colors.grey.shade500,
                                                                  //                                                   ),
                                                                  //                                                 ),
                                                                  //                                               ),
                                                                  //                                             ],
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                         SizedBox(height: 14),
                                                                  //                                         // Second chat session
                                                                  //                                         Container(
                                                                  //                                           padding: EdgeInsets.all(12),
                                                                  //                                           decoration: BoxDecoration(
                                                                  //                                             color: Colors.white,
                                                                  //                                             borderRadius: BorderRadius.circular(8),
                                                                  //                                             border: Border.all(color: Colors.grey.shade200),
                                                                  //                                           ),
                                                                  //                                           child: Column(
                                                                  //                                             crossAxisAlignment: CrossAxisAlignment.start,
                                                                  //                                             children: [
                                                                  //                                               Text(
                                                                  //                                                 "Chat session name To Store",
                                                                  //                                                 style: TextStyle(
                                                                  //                                                   fontSize: 14,
                                                                  //                                                   fontWeight: FontWeight.w500,
                                                                  //                                                   color: Color(0xff848484),
                                                                  //                                                 ),
                                                                  //                                               ),
                                                                  //                                               SizedBox(height: 24),
                                                                  //                                               Align(
                                                                  //                                                 alignment: Alignment.centerRight,
                                                                  //                                                 child: Text(
                                                                  //                                                   "02/05/2023",
                                                                  //                                                   style: TextStyle(
                                                                  //                                                     fontSize: 10,
                                                                  //                                                     color: Colors.grey.shade500,
                                                                  //                                                   ),
                                                                  //                                                 ),
                                                                  //                                               ),
                                                                  //                                             ],
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                       ],
                                                                  //                                     ),
                                                                  //                                   ),
                                                                  //                                 ],
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                           ],
                                                                  //                         ),
                                                                  //                       ),
                                                                  //                     ),
                                                                  //                   ),
                                                                  //                 ),
                                                                  //             ],
                                                                  //           ),
                                                                  //         ),

                                                                  //         // Third column with right-to-left animation
                                                                  //         SlideTransition(
                                                                  //           position:
                                                                  //               _thirdColumnAnimation,
                                                                  //           child: Column(
                                                                  //             children: [
                                                                  //               if (showAIIcons)
                                                                  //                 SizedBox(height: 10),
                                                                  //               // Third column - AI icons
                                                                  //               if (showAIIcons)
                                                                  //                 MouseRegion(
                                                                  //                   cursor:
                                                                  //                       SystemMouseCursors
                                                                  //                           .click,
                                                                  //                   onEnter: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isThirdColumnHovered =
                                                                  //                               true),
                                                                  //                   onExit: (_) =>
                                                                  //                       setState(() =>
                                                                  //                           isThirdColumnHovered =
                                                                  //                               false),
                                                                  //                   child:
                                                                  //                       GestureDetector(
                                                                  //                     onTap:
                                                                  //                         _handleSolutionsClick,
                                                                  //                     child: Container(
                                                                  //                       width: 212,
                                                                  //                       height: 284,
                                                                  //                       // padding: EdgeInsets.all(12),
                                                                  //                       decoration:
                                                                  //                           BoxDecoration(
                                                                  //                         // color: Colors.white,
                                                                  //                         borderRadius:
                                                                  //                             BorderRadius
                                                                  //                                 .circular(
                                                                  //                                     12),
                                                                  //                       ),
                                                                  //                       child:
                                                                  //                           Container(
                                                                  //                         decoration:
                                                                  //                             BoxDecoration(
                                                                  //                           borderRadius:
                                                                  //                               BorderRadius.circular(
                                                                  //                                   12),
                                                                  //                         ),
                                                                  //                         child: Stack(
                                                                  //                           children: [
                                                                  //                             // Background SVG image positioned in bottom layer with stack animation
                                                                  //                             Positioned
                                                                  //                                 .fill(
                                                                  //                               child:
                                                                  //                                   AnimatedBuilder(
                                                                  //                                 animation:
                                                                  //                                     _thirdBgController,
                                                                  //                                 builder:
                                                                  //                                     (context, child) {
                                                                  //                                   return Transform.scale(
                                                                  //                                     scale: _thirdBgScaleAnimation.value,
                                                                  //                                     child: FadeTransition(
                                                                  //                                       opacity: _thirdBgAnimation,
                                                                  //                                       child: AnimatedContainer(
                                                                  //                                         duration: Duration(milliseconds: 600),
                                                                  //                                         curve: Curves.easeInOut,
                                                                  //                                         transform: Matrix4.translationValues(
                                                                  //                                           (1 - _thirdBgAnimation.value) * -20 + (isThirdColumnHovered ? 8 : 0),
                                                                  //                                           (1 - _thirdBgAnimation.value) * 20,
                                                                  //                                           0,
                                                                  //                                         ),
                                                                  //                                         child: ClipRRect(
                                                                  //                                           borderRadius: BorderRadius.circular(12),
                                                                  //                                           child: SvgPicture.asset(
                                                                  //                                             'assets/images/bg-shape-edu-new.svg',
                                                                  //                                             fit: BoxFit.cover,
                                                                  //                                             alignment: Alignment.bottomRight,
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                       ),
                                                                  //                                     ),
                                                                  //                                   );
                                                                  //                                 },
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                             // Main content layer with fade animation
                                                                  //                             FadeTransition(
                                                                  //                               opacity:
                                                                  //                                   _thirdContentAnimation,
                                                                  //                               child:
                                                                  //                                   Column(
                                                                  //                                 mainAxisAlignment:
                                                                  //                                     MainAxisAlignment.center,
                                                                  //                                 children: [
                                                                  //                                   isImagesLoading
                                                                  //                                       ? Center(child: CircularProgressIndicator())
                                                                  //                                       : Container(
                                                                  //                                           width: 177,
                                                                  //                                           height: 261,
                                                                  //                                           // padding: EdgeInsets.all(10),
                                                                  //                                           decoration: BoxDecoration(
                                                                  //                                             color: Color(0xFFF2F6F8),
                                                                  //                                             borderRadius: BorderRadius.circular(12),
                                                                  //                                             border: Border.all(
                                                                  //                                               color: Color(0xFFDBE9F2), // Hex #DBE9F2
                                                                  //                                               width: 1, // 1px border
                                                                  //                                             ),
                                                                  //                                           ),
                                                                  //                                           child: Column(
                                                                  //                                             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                                  //                                             children: [
                                                                  //                                               // First row
                                                                  //                                               Row(
                                                                  //                                                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                                  //                                                 children: [
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 0 ? imageData['ai_icons'][0] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 1 ? imageData['ai_icons'][1] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                 ],
                                                                  //                                               ),
                                                                  //                                               // Second row
                                                                  //                                               Row(
                                                                  //                                                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                                  //                                                 children: [
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 2 ? imageData['ai_icons'][2] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 3 ? imageData['ai_icons'][3] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                 ],
                                                                  //                                               ),
                                                                  //                                               // Third row
                                                                  //                                               Row(
                                                                  //                                                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                                  //                                                 children: [
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 4 ? imageData['ai_icons'][4] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                   Image.asset(
                                                                  //                                                     imageData.containsKey('ai_icons') && imageData['ai_icons'].length > 5 ? imageData['ai_icons'][5] : 'assets/images/book-09.png',
                                                                  //                                                     width: 80,
                                                                  //                                                     height: 80,
                                                                  //                                                   ),
                                                                  //                                                 ],
                                                                  //                                               ),
                                                                  //                                             ],
                                                                  //                                           ),
                                                                  //                                         ),
                                                                  //                                 ],
                                                                  //                               ),
                                                                  //                             ),
                                                                  //                           ],
                                                                  //                         ),
                                                                  //                       ),
                                                                  //                     ),
                                                                  //                   ),
                                                                  //                 ),
                                                                  //             ],
                                                                  //           ),
                                                                  //         ),
                                                                  //       ],
                                                                  //     ),
                                                                  //   ],
                                                                  // ),
                                                                  // SizedBox(height: 20),
                                                                ],
                                                              ),
                                                            ),
                                                            Provider.of<WebHomeProviderStatic>(
                                                                        context)
                                                                    .isChatHistoryExpanded
                                                                ? Container(
                                                                    width: 150,
                                                                  )
                                                                : MediaQuery.of(context)
                                                                            .size
                                                                            .width >
                                                                        1200
                                                                    ? Expanded(
                                                                        child:
                                                                            SizedBox())
                                                                    : SizedBox(
                                                                        width: MediaQuery.of(context).size.width *
                                                                            0.1,
                                                                      ),
                                                          ],
                                                        ),
                                                        // Conditionally show WebMyLibraryScreen based on Inspirations click
                                                        if (showWebMyLibraryScreen)
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        60),
                                                            height: AppSpacing
                                                                .getResponsiveHeight(
                                                                    context),
                                                            child: WebMyLibraryScreen(
                                                                showNavigationBar:
                                                                    false),
                                                          ),

                                                        // Conditionally show WebSolutionsScreen based on Solutions click
                                                        if (showWebSolutionsScreen)
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        60),
                                                            height: AppSpacing
                                                                .getResponsiveHeight(
                                                                    context),
                                                            child: WebSolutionsScreen(
                                                                showNavigationBar:
                                                                    false),
                                                          ),

                                                        // Conditionally show ChatSessionList based on My Chats click
                                                        if (showChatSessionList)
                                                          Row(
                                                            children: [
                                                              Provider.of<WebHomeProviderStatic>(
                                                                          context)
                                                                      .isChatHistoryExpanded
                                                                  ? Container(
                                                                      width:
                                                                          150,
                                                                    )
                                                                  : MediaQuery.of(context)
                                                                              .size
                                                                              .width >
                                                                          1200
                                                                      ? Expanded(
                                                                          child:
                                                                              SizedBox())
                                                                      : SizedBox(
                                                                          width:
                                                                              MediaQuery.of(context).size.width * 0.1),
                                                              Expanded(
                                                                flex: 3,
                                                                child:
                                                                    LayoutBuilder(
                                                                  builder: (context,
                                                                      constraints) {
                                                                    return SizedBox(
                                                                      height: AppSpacing
                                                                          .getResponsiveHeight(
                                                                              context),
                                                                      child: ChatSessionList(
                                                                          showNavigationBar:
                                                                              false),
                                                                    );
                                                                  },
                                                                ),
                                                              ),
                                                              Provider.of<WebHomeProviderStatic>(
                                                                          context)
                                                                      .isChatHistoryExpanded
                                                                  ? Container(
                                                                      width:
                                                                          150,
                                                                    )
                                                                  : MediaQuery.of(context)
                                                                              .size
                                                                              .width >
                                                                          1200
                                                                      ? Expanded(
                                                                          child:
                                                                              SizedBox())
                                                                      : SizedBox(
                                                                          width:
                                                                              MediaQuery.of(context).size.width * 0.1,
                                                                        )
                                                            ],
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        else
                                          Expanded(
                                            child: Row(
                                              children: [
                                                chatHistory(),
                                                showSidePanel
                                                    ? Container(
                                                        width: AppSpacing.lg)
                                                    : Provider.of<WebHomeProviderStatic>(
                                                                    context,
                                                                    listen:
                                                                        false)
                                                                .isChatHistoryExpanded ||
                                                            Provider.of<WebHomeProviderStatic>(
                                                                    context,
                                                                    listen:
                                                                        false)
                                                                .showStatus
                                                        ? Container(
                                                            width: Provider.of<
                                                                            WebHomeProviderStatic>(
                                                                        context,
                                                                        listen:
                                                                            false)
                                                                    .showStatusArtifacts
                                                                ? AppSpacing
                                                                    .spaceBetweenMainStatic
                                                                : AppSpacing
                                                                    .spaceBetweenMainStatic,
                                                          )
                                                        : MediaQuery.of(context)
                                                                    .size
                                                                    .width >
                                                                600
                                                            ? Expanded(
                                                                child:
                                                                    SizedBox())
                                                            : SizedBox.shrink(),
                                                Expanded(
                                                  flex: 12,
                                                  child: Column(
                                                    children: [
                                                      Expanded(
                                                        child: Builder(
                                                          builder: (context) {
                                                            Logger.info(
                                                                'Building ListView with ${messages.length} messages');
                                                            return SelectionArea(
                                                              child: ListView
                                                                  .builder(
                                                                // key: ValueKey<int>(messages
                                                                //     .length), // Add key to force rebuild
                                                                controller:
                                                                    _chatScrollController,
                                                                shrinkWrap:
                                                                    true,
                                                                padding:
                                                                    EdgeInsets.all(
                                                                        AppSpacing
                                                                            .md),
                                                                itemCount: messages
                                                                        .length +
                                                                    (isLoading
                                                                        ? 1
                                                                        : 0),
                                                                itemBuilder:
                                                                    (context,
                                                                        index) {
                                                                  if (index ==
                                                                      messages
                                                                          .length) {
                                                                    // Show loading indicator
                                                                    return NSLThinkingMessageLoader();
                                                                  }

                                                                  final message =
                                                                      messages[
                                                                          index];
                                                                  Logger.info(
                                                                      'Building message bubble for index $index: ${message.content}');
                                                                  return ChatMessageBubbleStatic(
                                                                    message:
                                                                        message,
                                                                    // nslThinkingExpanded:
                                                                    //     nslThinkingExpanded,
                                                                    index:
                                                                        index,
                                                                    isLastItem: index ==
                                                                            messages.length -
                                                                                1
                                                                        ? true
                                                                        : false,
                                                                    multimediaService:
                                                                        _multimediaService,
                                                                    parentState:
                                                                        this,
                                                                    onNewLine:
                                                                        () {
                                                                      _scrollToBottom();
                                                                    },
                                                                    onComplete:
                                                                        () {
                                                                      if (mounted)
                                                                        _scrollToBottom();
                                                                    },
                                                                  );
                                                                },
                                                              ),
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      provider.isProjectCreated
                                                          ? SizedBox.shrink()
                                                          : NewExistingProjectModel(
                                                              onNewProjectSelected:
                                                                  _handleNewProjectSelected,
                                                              onExistingProjectSelected:
                                                                  _handleExistingProjectSelected,
                                                            ),
                                                      SizedBox(height: 20),
                                                      Column(
                                                        children: [
                                                          // Only show chat field if not showing side panel
                                                          (!showSidePanel)
                                                              ? Padding(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          8.0),
                                                                  child: chatField(
                                                                      context,
                                                                      dropdownAbove:
                                                                          true),
                                                                )
                                                              : SizedBox(),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                showSidePanel
                                                    ? Container(
                                                        width: AppSpacing.lg)
                                                    : Provider.of<WebHomeProviderStatic>(
                                                                    context)
                                                                .isChatHistoryExpanded ||
                                                            Provider.of<WebHomeProviderStatic>(
                                                                    context,
                                                                    listen:
                                                                        false)
                                                                .showStatus
                                                        ? Container(
                                                            width: Provider.of<
                                                                            WebHomeProviderStatic>(
                                                                        context,
                                                                        listen:
                                                                            false)
                                                                    .showStatusArtifacts
                                                                ? AppSpacing
                                                                    .spaceBetweenMainStatic
                                                                : AppSpacing
                                                                    .spaceBetweenMainStatic)
                                                        : MediaQuery.of(context)
                                                                    .size
                                                                    .width >
                                                                1200
                                                            ? Expanded(
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .end,
                                                                  children: [
                                                                    // // Toggle button for OCR panel when it has content
                                                                    // if (isOcrExist ||
                                                                    //     (messages.isNotEmpty &&
                                                                    //         selectedQuickMessage ==
                                                                    //             "Solution" &&
                                                                    //         !showSidePanel))
                                                                    //   OcrPanelToggleButton(
                                                                    //     isShown:
                                                                    //         showOcrPanel || showSidePanel,
                                                                    //     onToggle: toggleOcrPanel,
                                                                    //   ),
                                                                    if (selectedQuickMessage ==
                                                                            "Solution" &&
                                                                        messages.length >=
                                                                            4)
                                                                      OcrPanelToggleButton(
                                                                          isShown: Provider.of<WebHomeProviderStatic>(context, listen: false)
                                                                              .showStatus,
                                                                          onToggle: () =>
                                                                              Provider.of<WebHomeProviderStatic>(context, listen: false).toggleStatus())
                                                                  ],
                                                                ),
                                                              )
                                                            : SizedBox.shrink()
                                              ],
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),

                                // Side panel for role, entity, workflow details, or OCR results
                                if (showSidePanel ||
                                    showOcrPanel ||
                                    Provider.of<WebHomeProviderStatic>(context,
                                            listen: false)
                                        .showStatusArtifacts)
                                  ResizablePanel(
                                    width: sidePanelWidth,
                                    minWidth: minSidePanelWidth,
                                    maxWidth: maxSidePanelWidth,
                                    handlePosition: ResizeHandlePosition.left,
                                    onResize: (newWidth) {
                                      setState(() {
                                        sidePanelWidth = newWidth;
                                      });
                                    },
                                    child: Provider.of<WebHomeProviderStatic>(
                                                context,
                                                listen: false)
                                            .showStatusArtifacts
                                        ? SolutionArtifactsPanel(
                                            solutionSessionModel: Provider.of<
                                                        WebHomeProviderStatic>(
                                                    context,
                                                    listen: false)
                                                .selectedSolutionSessionModel!,
                                            onClose: () {
                                              Provider.of<WebHomeProviderStatic>(
                                                      context,
                                                      listen: false)
                                                  .toggleStatusArtifacts(null);
                                            },
                                          )
                                        : showOcrPanel
                                            ? OcrTextSidePanel(
                                                ocrText: ocrText,
                                                fileName: ocrFileName,
                                              )
                                            : selectedRole != null
                                                ? _buildRoleDetailsPanel(
                                                    selectedRole!)
                                                : selectedEntity != null
                                                    ? _buildEntityDetailsPanel(
                                                        selectedEntity!)
                                                    : selectedWorkflow != null
                                                        ? WorkflowSidePanel(
                                                            workflowId:
                                                                selectedWorkflow![
                                                                    'id'],
                                                            onClose: () {},
                                                            ocrButton:
                                                                OcrPanelToggleButton(
                                                              isShown:
                                                                  showOcrPanel ||
                                                                      showSidePanel,
                                                              onToggle:
                                                                  toggleOcrPanel,
                                                            ),
                                                          )
                                                        : Container(),
                                  ),
                              ],
                            ),
                          )
                        : ManualCreationStaticScreen(),
                  ),
                ],
              ),
            ),
            // Side panel on the right (restored to original position)
            showSidePanel
                ? Flexible(
                    flex: 1,
                    child: provider.showStatusArtifacts
                        ? SolutionArtifactsPanel(
                            solutionSessionModel:
                                provider.selectedSolutionSessionModel!,
                            onClose: () {
                              provider.toggleStatusArtifacts(null);
                            },
                          )
                        : showOcrPanel
                            ? OcrTextSidePanel(
                                ocrText: ocrText,
                                fileName: ocrFileName,
                              )
                            : selectedRole != null
                                ? _buildRoleDetailsPanel(selectedRole!)
                                : selectedEntity != null
                                    ? _buildEntityDetailsPanel(selectedEntity!)
                                    : selectedWorkflow != null
                                        ? WorkflowSidePanel(
                                            workflowId: selectedWorkflow!['id'],
                                            onClose: () {},
                                            ocrButton: OcrPanelToggleButton(
                                              isShown:
                                                  showOcrPanel || showSidePanel,
                                              onToggle: toggleOcrPanel,
                                            ),
                                          )
                                        : Container(),
                  )
                : SizedBox.shrink(),
            // Show solutionDetailsPanel only when showSolutionDetailsPanel is true
            if (provider.showGlobalLibrary) globalLibrary(context),
            if (provider.showSolutionDetailsPanel) solutionDetailsPanel(context)
          ],
        );
      },
    );
  }

  Widget globalLibrary(context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width /
          3.5, // Dynamic width based on screen size
      color: Colors.white,
      child: Column(
        children: [
          // Top Navigation Sectio
          // Global Library Accordion Flow
          Expanded(
            child: const GlobalLibraryAccordionFlow(),
          ),
        ],
      ),
    );
  }
}

// Chat history item widget with hover effects
class _ChatHistoryItemWidget extends StatefulWidget {
  final Map<String, dynamic> chat;
  final Color backgroundColor;
  // final IconData chatTypeIcon;
  final VoidCallback onTap;
  final Function formatTimestamp;
  final bool isExpanded;

  const _ChatHistoryItemWidget({
    required this.chat,
    required this.backgroundColor,
    // required this.chatTypeIcon,
    required this.onTap,
    required this.formatTimestamp,
    required this.isExpanded,
  });

  @override
  State<_ChatHistoryItemWidget> createState() => _ChatHistoryItemWidgetState();
}

class _ChatHistoryItemWidgetState extends State<_ChatHistoryItemWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // If not expanded, don't render anything
    if (!widget.isExpanded) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: InkWell(
            onTap: widget.onTap,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
            child: Container(
              // margin: EdgeInsets.symmetric(horizontal: 2),
              padding: EdgeInsets.symmetric(horizontal: 2, vertical: 7),
              decoration: BoxDecoration(
                color: isHovered ? Color(0xffF2F2F2) : Colors.transparent,
                // border: Border(
                //   bottom: BorderSide(
                //     color: Colors.grey.shade300,
                //     width: 1,
                //   ),
                // ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Chat type icon
                  // Container(
                  //   margin: EdgeInsets.only(right: 8, top: 2),
                  //   padding: EdgeInsets.all(4),
                  //   decoration: BoxDecoration(
                  //     color:
                  //         Colors.white.withValues(alpha: 179), // 0.7 * 255 = ~179
                  //     borderRadius: BorderRadius.circular(4),
                  //   ),
                  //   child:
                  //   //  Icon(
                  //   //   widget.chatTypeIcon,
                  //   //   size: 16,
                  //   //   color: Theme.of(context).colorScheme.primary,
                  //   // ),
                  // ),

                  // Chat content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Padding(
                          padding: EdgeInsets.only(left: AppSpacing.xs),
                          child: Text(
                            widget.chat['title'] ?? 'Untitled Chat',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              // fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // SizedBox(height: 4),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        //  Divider(

        //               thickness: 1,
        //               color: Colors.grey.shade300,
        //             ),
      ],
    );
  }
}

// Relationship Entity Card widget
class _RelationshipEntityCard extends StatefulWidget {
  final Entity entity;
  final Entity parentEntity;
  final String relationshipType;
  final VoidCallback onTap;

  const _RelationshipEntityCard({
    required this.entity,
    required this.parentEntity,
    required this.relationshipType,
    required this.onTap,
  });

  @override
  State<_RelationshipEntityCard> createState() =>
      _RelationshipEntityCardState();
}

class _RelationshipEntityCardState extends State<_RelationshipEntityCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Get access to the parent state
    final WebHomeScreenChatStaticState? parentState =
        context.findAncestorStateOfType<WebHomeScreenChatStaticState>();

    // Create a unique key for this entity
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'relationshipEntity_${widget.entity.id}');

    return Column(
      children: [
        // Entity card with hover effect
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              key: entityCardKey,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isHovered ? Colors.grey.shade50 : Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  // Checkbox
                  Container(
                    padding: EdgeInsets.only(
                      right: AppSpacing.xxs,
                      top: AppSpacing.xs,
                      bottom: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        right:
                            BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                    ),
                    child: CustomCheckbox(
                      initialValue: parentState?.globalEntitiesData
                              .getEntityCheckedState(widget.entity.id ?? '') ??
                          false,
                      onChanged: (bool value) {
                        if (parentState != null) {
                          parentState.setState(() {
                            parentState.globalEntitiesData
                                .updateEntityCheckedState(
                                    widget.entity.id ?? '', value);
                          });
                        }
                      },
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                  SizedBox(width: AppSpacing.xs),

                  // Entity title and attributes
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          widget.entity.title ?? 'Untitled',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 13,
                            fontFamily: "TiemposText",
                          ),
                        ),

                        // Attributes
                        if (widget.entity.attributeString != null &&
                            widget.entity.attributeString!.isNotEmpty)
                          Row(
                            children: [
                              Text(
                                'has ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                  fontFamily: "TiemposText",
                                  fontSize: 12,
                                ),
                              ),
                              Expanded(
                                child: parentState
                                        ?._buildAttributeStringWithPrimaryKeys(
                                      widget.entity,
                                      allowEllipsis: true,
                                    ) ??
                                    Text(widget.entity.attributeString ?? ''),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),

                  // Relationship indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: parentState
                              ?.getRelationColor(widget.entity.relationType)
                              .withAlpha(25) ??
                          Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          parentState?.getRelationIcon(
                                  widget.entity.relationType) ??
                              Icons.link,
                          size: 12,
                          color: parentState?.getRelationColor(
                                  widget.entity.relationType) ??
                              Colors.grey.shade700,
                        ),
                        SizedBox(width: 3),
                        Text(
                          widget.entity.relationType?.toUpperCase() ?? '',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: parentState?.getRelationColor(
                                    widget.entity.relationType) ??
                                Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class EntityProfileCard extends StatelessWidget {
  final String id;
  final String version;
  final String createdBy;
  final DateTime createdDate;
  final String modifiedBy;
  final DateTime modifiedDate;
  final Map<String, dynamic>? properties;
  final VoidCallback? onEdit;
  final String title;

  const EntityProfileCard({
    super.key,
    required this.id,
    required this.version,
    required this.createdBy,
    required this.createdDate,
    required this.modifiedBy,
    required this.modifiedDate,
    this.properties,
    this.onEdit,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // Format dates for the UserProfileCard
    final String formattedCreatedDate = _formatDate(createdDate);
    final String formattedModifiedDate = _formatDate(modifiedDate);

    // Use our reusable UserProfileCard component
    return UserProfileCard(
      id: id,
      version: version,
      displayName: title,
      createdBy: createdBy,
      createdDate: formattedCreatedDate,
      modifiedBy: modifiedBy,
      modifiedDate: formattedModifiedDate,
      roleTitle: 'Entity Type',
      roleDescription:
          'Core Business Entity - Represents an individual or organisation that purchases goods.',

      width: MediaQuery.of(context).size.width / 3, // Match the original width
      leftMargin: 0, // No left margin needed in this context
    );
  }
}

class WorkflowProfileCard extends StatelessWidget {
  final String id;
  final String version;
  final String createdBy;
  final String createdDate;
  final String modifiedBy;
  final String modifiedDate;
  final Map<String, dynamic>? properties;
  final VoidCallback? onEdit;
  final String title;

  const WorkflowProfileCard({
    super.key,
    required this.id,
    required this.version,
    required this.createdBy,
    required this.createdDate,
    required this.modifiedBy,
    required this.modifiedDate,
    this.properties,
    this.onEdit,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // Use our reusable UserProfileCard component
    return UserProfileCard(
      id: id,
      version: version,
      displayName: title,
      createdBy: createdBy,
      createdDate: createdDate,
      modifiedBy: modifiedBy,
      modifiedDate: modifiedDate,
      roleTitle: 'Workflow',
      roleDescription:
          'Core Business Process - Represents a sequence of steps for completing a business task.',
      profileImagePath: 'assets/images/user_profile_image.png',
      headerColor: Color(0xFF0058FF), // Blue header
      width: MediaQuery.of(context).size.width / 3, // Match the original width
      leftMargin: 0, // No left margin needed in this context
    );
  }
}

class _HoverArrowIcon extends StatefulWidget {
  final VoidCallback onTap;
  final Animation<double> iconTurn;

  const _HoverArrowIcon({
    required this.onTap,
    required this.iconTurn,
  });

  @override
  State<_HoverArrowIcon> createState() => _HoverArrowIconState();
}

class _HoverArrowIconState extends State<_HoverArrowIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: RotationTransition(
          turns: widget.iconTurn,
          child: SvgPicture.asset(
            'assets/images/arrow_down.svg',
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              isHovered ? Color(0xff0058FF) : Colors.grey.shade600,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}

// Custom painter for vertical dotted lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      // Draw a small dash
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      // Move past the dash and gap
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(DottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

// Custom painter for horizontal dotted lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      // Draw a small dash
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      // Move past the dash and gap
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(HorizontalDottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

// A stateful timer widget that starts immediately
class _RecordingTimer extends StatefulWidget {
  @override
  State<_RecordingTimer> createState() => _RecordingTimerState();
}

class _RecordingTimerState extends State<_RecordingTimer> {
  int _seconds = 0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    // Start the timer immediately
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _startTimer() {
    // Start immediately with 0 seconds
    setState(() {
      _seconds = 0;
    });

    // Update every second
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _seconds++;
      });
    });
  }

  String _formatDuration() {
    final minutes = (_seconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formatDuration(),
      style: FontManager.getCustomStyle(
        fontSize: FontManager.s18,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}

String _formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year}';
}

// Add button with hover effect for chat history
class _HoverAddButton extends StatefulWidget {
  final VoidCallback onTap;

  const _HoverAddButton({
    required this.onTap,
  });

  @override
  State<_HoverAddButton> createState() => _HoverAddButtonState();
}

class _HoverAddButtonState extends State<_HoverAddButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isHovered
                ? Color(0xffE4EDFF)
                : Colors.transparent, // Light blue background on hover
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Icon(
              Icons.add,
              size: 16,
              color: isHovered
                  ? Color(0xff0058FF)
                  : Colors.grey.shade600, // Blue icon on hover
            ),
          ),
        ),
      ),
    );
  }
}

// Expand/collapse button with hover effect for chat history
class _HoverExpandButton extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback onTap;
  final String tooltipMessage;

  const _HoverExpandButton({
    required this.isExpanded,
    required this.onTap,
    this.tooltipMessage = '',
  });

  @override
  State<_HoverExpandButton> createState() => _HoverExpandButtonState();
}

class _HoverExpandButtonState extends State<_HoverExpandButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        message: widget.tooltipMessage,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isHovered
                  ? Color(0xffE4EDFF)
                  : Colors.transparent, // Light blue background on hover
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: widget.isExpanded
                  ? Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..scale(-1.0, 1.0, 1.0), // Flip horizontally
                      child: Icon(
                        Icons.login,
                        size: 16,
                        color: isHovered
                            ? Color(0xff0058FF)
                            : Colors.grey.shade600, // Blue icon on hover
                      ),
                    )
                  : Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: isHovered
                          ? Color(0xff0058FF)
                          : Colors.grey.shade600, // Blue icon on hover
                    ),
            ),
          ),
        ),
      ),
    );
  }
}

// Create a stateful widget for column items to handle selection
class ColumnItem extends StatefulWidget {
  final String svgPath;
  final String label;
  final VoidCallback? onTap;
  final bool isSelected;

  const ColumnItem({
    super.key,
    required this.svgPath,
    required this.label,
    this.onTap,
    this.isSelected = false,
  });

  @override
  State<ColumnItem> createState() => _ColumnItemState();
}

class _ColumnItemState extends State<ColumnItem> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          Logger.info(
              "${widget.label} clicked - Selected: ${widget.isSelected}");

          // Call the custom callback if provided
          if (widget.onTap != null) {
            widget.onTap!();
          }
        },
        child:
            _buildRow(), // Always use _buildRow() which now has container with border
      ),
    );
  }

  // Build the row content
  Widget _buildRow() {
    return Container(
      margin: EdgeInsets.only(right: 10, left: 20),
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.isSelected ? Color(0xFF0058FF) : Colors.transparent,
          width: 1,
        ),
        color: widget.isSelected ? Colors.transparent : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            widget.svgPath,
            width: 16,
            height: 16,
            fit: BoxFit.contain,
            colorFilter: ColorFilter.mode(
              widget.isSelected ? Color(0xFF0058FF) : Color(0xFF797979),
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: AppSpacing.xs),
          // Text with color change
          Text(
            widget.label,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.w400,
              color: widget.isSelected ? Color(0xFF0058FF) : Color(0xff797979),
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to create column items
Widget buildColumnItem(String svgPath, String label,
    {VoidCallback? onTap, bool isSelected = false}) {
  return ColumnItem(
    svgPath: svgPath,
    label: label,
    onTap: onTap,
    isSelected: isSelected,
  );
}
