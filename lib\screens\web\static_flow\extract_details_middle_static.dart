import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_table.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

class ExtractDetailsMiddleStatic extends StatefulWidget {
  const ExtractDetailsMiddleStatic({super.key});

  @override
  State<ExtractDetailsMiddleStatic> createState() =>
      _ExtractDetailsMiddleStaticState();
}

class _ExtractDetailsMiddleStaticState
    extends State<ExtractDetailsMiddleStatic> {
  OverlayEntry? _overlayEntry;
  final Map<String, LayerLink> _layerLinks = {};
  String? _currentHoveredObject;
  late AccordionController _accordionController;

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _removeOverlay();
    super.dispose();
  }

  LayerLink _getLayerLink(String objectTitle) {
    if (!_layerLinks.containsKey(objectTitle)) {
      _layerLinks[objectTitle] = LayerLink();
    }
    return _layerLinks[objectTitle]!;
  }

  void _showOverlay(String objectTitle) {
    _removeOverlay();
    _currentHoveredObject = objectTitle;
    _overlayEntry = _createOverlayEntry(objectTitle);
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _currentHoveredObject = null;
  }

  OverlayEntry _createOverlayEntry(String objectTitle) {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: 280,
        child: CompositedTransformFollower(
          link: _getLayerLink(objectTitle),
          showWhenUnlinked: false,
          offset: const Offset(-250, 25),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    'This Object is already exists in your library. You need to rename the objects to proceed.',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.grey[700]!,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () => _removeOverlay(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                            side: BorderSide(color: Colors.grey[500]!),
                          ),
                        ),
                        child: Text(
                          'Continue',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.grey[600]!,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => _removeOverlay(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Resolve',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            provider.isAIMode ? 'Extracted Details' : 'Objects',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.w400,
              height: 1,
            ),
          ),

          // AI/Manual Toggle - positioned to the right
          _buildAIManualToggle(context, provider),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              provider.toggleAIMode();
              Provider.of<ManualCreationProvider>(context, listen: false)
                  .handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          provider.isAIMode ? 'Form' : 'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildExtractedDetailsTab(context);
    } else {
      return _buildObjectsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(6.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Expansion panels for objects in extracted details
          _buildObjectExpansionPanel(context, 'Object: Customer'),

          _buildObjectExpansionPanel(context, 'Object: Product'),

          _buildObjectExpansionPanel(context, 'Object: Order'),
        ],
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EntityTable(
            provider:
                Provider.of<ManualCreationProvider>(context, listen: false),
            onEntitySelected: (entity) {
              Provider.of<ManualCreationProvider>(context, listen: false);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(
      BuildContext context, String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Color(0xFF0058FF),
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                if (subtitle != title) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(objectTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            // borderRadius: BorderRadius.circular(4),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true, // slightly tighter by default
              child: ExpansionTile(
                tilePadding: const EdgeInsets.symmetric(horizontal: 4,  vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(objectTitle, expanded),
                trailing: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 16,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        objectTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    CompositedTransformTarget(
                      link: _getLayerLink(objectTitle),
                      child: MouseRegion(
                        onEnter: (_) => _showOverlay(objectTitle),
                        onExit: (_) => _removeOverlay(),
                        child: const Icon(Icons.notifications,
                            color: Colors.red, size: 16),
                      ),
                    ),
                  ],
                ),
                children: [
                  _buildObjectDetailsSection(context, objectTitle),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Books accordion style - recreated from global_library_accordion_flow
  Widget _buildBooksAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);

    // Get status and count based on item title
    final statusInfo = _getStatusInfo(item.title);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
            color:
                isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
            width: isExpanded ? 2 : 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  // Title
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.title,
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status badge positioned on the right
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusInfo['backgroundColor'],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            statusInfo['status'],
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: statusInfo['textColor'],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Count
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      statusInfo['count'],
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show nested accordion items
                  _buildNestedAccordionItems(context),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: [
          // Object Details accordion
          _buildSimpleAccordionItem(
            context,
            'Object Details',
            'Partial Completion',
            '3 Entity Detected',
            const Color(0xFFFEF3C7),
            const Color(0xFF92400E),
            false,
          ),

          // Attributes Details accordion with table
          _buildSimpleAccordionItem(
            context,
            'Attributes Details',
            'Completed',
            '25 Attributes',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
            true,
          ),

          // Other accordion items
          _buildSimpleAccordionItem(
            context,
            'Entity Relationships',
            'Partial Completion',
            '0 rules Configured',
            const Color(0xFFFEF3C7),
            const Color(0xFF92400E),
            false,
          ),
          _buildSimpleAccordionItem(
            context,
            'Attribute Business Rules',
            'Missing',
            '0 Configure',
            const Color(0xFFFEE2E2),
            const Color(0xFF991B1B),
            false,
          ),
          _buildSimpleAccordionItem(
            context,
            'Enumerated Values',
            'Missing',
            '0 Configure',
            const Color(0xFFFEE2E2),
            const Color(0xFF991B1B),
            false,
          ),
          _buildSimpleAccordionItem(
            context,
            'System Permissions',
            'Not Configured',
            '0 Configure',
            const Color(0xFFFEE2E2),
            const Color(0xFF991B1B),
            false,
          ),
          _buildSimpleAccordionItem(
            context,
            'Security Classification',
            'Not Configured',
            '0 Configure',
            const Color(0xFFFEE2E2),
            const Color(0xFF991B1B),
            false,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text
                        Expanded(
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                            overflow:
                                TextOverflow.ellipsis, // Handles long titles
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: backgroundColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            status,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: showAttributeTable
                  ? _buildAttributeConfigurationTable(context)
                  : _buildPlaceholderContent(context, title),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNestedAccordionItems(BuildContext context) {
    return Column(
      children: [
        _buildNestedAccordionItem(
          context,
          'Attributes Details',
          'Completed',
          '25 Attributes',
          const Color(0xFFD1FAE5),
          const Color(0xFF065F46),
          true,
        ),
        _buildNestedAccordionItem(
          context,
          'Entity Relationships',
          'Partial Completion',
          '0 rules Configured',
          const Color(0xFFFEF3C7),
          const Color(0xFF92400E),
          false,
        ),
        _buildNestedAccordionItem(
          context,
          'Attribute Business Rules',
          'Missing',
          '0 Configure',
          const Color(0xFFFEE2E2),
          const Color(0xFF991B1B),
          false,
        ),
        _buildNestedAccordionItem(
          context,
          'Enumerated Values',
          'Missing',
          '0 Configure',
          const Color(0xFFFEE2E2),
          const Color(0xFF991B1B),
          false,
        ),
        _buildNestedAccordionItem(
          context,
          'System Permissions',
          'Not Configured',
          '0 Configure',
          const Color(0xFFFEE2E2),
          const Color(0xFF991B1B),
          false,
        ),
        _buildNestedAccordionItem(
          context,
          'Security Classification',
          'Not Configured',
          '0 Configure',
          const Color(0xFFFEE2E2),
          const Color(0xFF991B1B),
          false,
        ),
      ],
    );
  }

  Widget _buildNestedAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('nested_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Row(
                children: [
                  // Title
                  Text(
                    title,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight:
                          isExpanded ? FontWeight.w600 : FontWeight.w500,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Status badge right after title
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      status,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyInter,
                        color: textColor,
                      ),
                    ),
                  ),
                  // Spacer to push count and arrow to the right
                  const Spacer(),
                  // Count
                  Text(
                    count,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded && showAttributeTable) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildAttributeConfigurationTable(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAttributeConfigurationTable(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add Attribute button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddAttributeModal(context),
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add Attribute'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  elevation: 0,
                  textStyle: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with horizontal scrolling
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Column(
                children: [
                  // Table header
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF9FAFB),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(6),
                        topRight: Radius.circular(6),
                      ),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 150,
                          child: Text(
                            'ATTRIBUTE NAME',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 150,
                          child: Text(
                            'DISPLAY NAME',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 120,
                          child: Text(
                            'DATA TYPE',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 100,
                          child: Text(
                            'REQUIRED',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 100,
                          child: Text(
                            'UNIQUE',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 80,
                          child: Text(
                            'ACTIONS',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              
                  // Table rows with vertical scrolling
                  Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildAttributeFormRow(context, 0, 'customer_id', 'Customer ID', 'string', 'YES', 'YES'),
                          _buildAttributeFormRow(context, 1, 'email', 'Email Address', 'string', 'YES', 'YES'),
                          _buildAttributeFormRow(context, 2, 'name', 'Full Name', 'string', 'YES', 'NO'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeRow(
    BuildContext context,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              attributeName,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              displayName,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              dataType,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              required,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              unique,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeFormRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Attribute Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: attributeName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),

          // Display Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: displayName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
              ),
            ),
          ),
          
          // Data Type Dropdown
          Container(
            width: 120,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: dataType,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['string', 'number', 'boolean', 'date'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),
          
          // Required Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: required,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),
          
          // Unique Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: unique,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),
          
          // Delete Action
          Container(
            width: 80,
            alignment: Alignment.center,
            child: IconButton(
              onPressed: () {
                // Handle delete
              },
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddAttributeModal(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Attribute Configuration',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields in 2x3 grid layout
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left column
                    Expanded(
                      child: Column(
                        children: [
                          _buildModalFormField(
                              context, 'Attribute Name', 'attribute_name'),
                          const SizedBox(height: 20),
                          _buildModalDropdownField(context, 'Data Type',
                              ['string', 'number', 'boolean', 'date']),
                          const SizedBox(height: 20),
                          _buildModalDropdownField(
                              context, 'Unique', ['No', 'Yes']),
                          const SizedBox(height: 20),
                          _buildModalFormField(
                              context, 'Description', 'Description'),
                        ],
                      ),
                    ),
                    const SizedBox(width: 24),
                    // Right column
                    Expanded(
                      child: Column(
                        children: [
                          _buildModalFormField(
                              context, 'Display Name', 'Display Name'),
                          const SizedBox(height: 20),
                          _buildModalDropdownField(
                              context, 'Required', ['No', 'Yes']),
                          const SizedBox(height: 20),
                          _buildModalFormField(
                              context, 'Default Value', 'Default Value'),
                          const SizedBox(height: 20),
                          _buildModalFormField(
                              context, 'Helper text', 'Helper text'),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        // Handle add attribute logic here
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Apply This',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModalFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value: options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlaceholderTitle(title),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddModal(context, title),
                icon: const Icon(Icons.add, size: 16),
                label: Text(_getAddButtonText(title)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  elevation: 0,
                  textStyle: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with horizontal scrolling
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                // constraints: BoxConstraints(minWidth: _getFormTableMinWidth(title)),
                child: Column(
                  children: [
                    // Table header
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                      decoration: const BoxDecoration(
                        color: Color(0xFFF9FAFB),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                      ),
                      child: _buildFormTableHeader(context, title),
                    ),

                    // Table rows with vertical scrolling
                    Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: SingleChildScrollView(
                        child: Column(
                          children: _buildFormTableRows(context, title),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context, String title) {
    List<String> headers = _getTableHeaders(title);
    List<int> flexValues = _getColumnFlexValues(title);

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        int flex = flexValues[index];

        return Expanded(
          flex: flex,
          child: Text(
            header,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelSmall(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.grey[700],
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);
    List<int> flexValues = _getColumnFlexValues(title);

    return rowsData.map((rowData) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          children: rowData.asMap().entries.map((entry) {
            int index = entry.key;
            String data = entry.value;
            int flex = flexValues[index];

            return Expanded(
              flex: flex,
              child: Text(
                data,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
        ),
      );
    }).toList();
  }

  List<int> _getColumnFlexValues(String title) {
    switch (title) {
      case 'Object Details':
        return [
          3,
          3,
          2,
          2,
          4
        ]; // PROPERTY NAME, VALUE, TYPE, REQUIRED, DESCRIPTION
      case 'Entity Relationships':
        return [
          3,
          3,
          2,
          2,
          2
        ]; // RELATIONSHIP NAME, TARGET ENTITY, TYPE, CARDINALITY, STATUS
      case 'Attribute Business Rules':
        return [
          3,
          2,
          3,
          2,
          2
        ]; // RULE NAME, ATTRIBUTE, CONDITION, ACTION, PRIORITY
      case 'Enumerated Values':
        return [
          3,
          4,
          2,
          3,
          2
        ]; // ENUM NAME, VALUES, DEFAULT, DESCRIPTION, STATUS
      case 'System Permissions':
        return [
          3,
          2,
          2,
          3,
          2
        ]; // PERMISSION NAME, ROLE, ACCESS LEVEL, RESOURCE, STATUS
      case 'Security Classification':
        return [
          3,
          2,
          3,
          3,
          2
        ]; // CLASSIFICATION, LEVEL, ATTRIBUTES, RESTRICTIONS, STATUS
      default:
        return [3, 3, 2, 2]; // NAME, VALUE, TYPE, STATUS
    }
  }

  List<String> _getTableHeaders(String title) {
    switch (title) {
      case 'Object Details':
        return ['PROPERTY NAME', 'VALUE', 'TYPE', 'REQUIRED', 'DESCRIPTION'];
      case 'Entity Relationships':
        return [
          'RELATIONSHIP NAME',
          'TARGET ENTITY',
          'TYPE',
          'CARDINALITY',
          'STATUS'
        ];
      case 'Attribute Business Rules':
        return ['RULE NAME', 'ATTRIBUTE', 'CONDITION', 'ACTION', 'PRIORITY'];
      case 'Enumerated Values':
        return ['ENUM NAME', 'VALUES', 'DEFAULT', 'DESCRIPTION', 'STATUS'];
      case 'System Permissions':
        return [
          'PERMISSION NAME',
          'ROLE',
          'ACCESS LEVEL',
          'RESOURCE',
          'STATUS'
        ];
      case 'Security Classification':
        return [
          'CLASSIFICATION',
          'LEVEL',
          'ATTRIBUTES',
          'RESTRICTIONS',
          'STATUS'
        ];
      default:
        return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
    }
  }

  List<double> _getColumnWidths(String title) {
    switch (title) {
      case 'Object Details':
        return [150, 150, 100, 100, 200];
      case 'Entity Relationships':
        return [180, 150, 140, 120, 110];
      case 'Attribute Business Rules':
        return [150, 120, 150, 120, 100];
      case 'Enumerated Values':
        return [150, 200, 100, 150, 100];
      case 'System Permissions':
        return [150, 120, 120, 150, 100];
      case 'Security Classification':
        return [150, 100, 150, 150, 100];
      default:
        return [150, 150, 100, 100];
    }
  }

  double _getTableMinWidth(String title) {
    List<double> widths = _getColumnWidths(title);
    return widths.reduce((a, b) => a + b);
  }

  List<List<String>> _getTableData(String title) {
    switch (title) {
      case 'Object Details':
        return [
          [
            'object_name',
            'Customer',
            'string',
            'YES',
            'Primary object identifier'
          ],
          [
            'created_date',
            '2024-01-15',
            'date',
            'YES',
            'Object creation timestamp'
          ],
          ['version', '1.0', 'string', 'NO', 'Object version number'],
        ];
      case 'Entity Relationships':
        return [
          ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
          ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
          ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
        ];
      case 'Attribute Business Rules':
        return [
          ['email_validation', 'email', 'format = email', 'reject', 'High'],
          ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
          ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
        ];
      case 'Enumerated Values':
        return [
          [
            'status_enum',
            'Active, Inactive, Pending',
            'Active',
            'Customer status options',
            'Active'
          ],
          [
            'type_enum',
            'Individual, Corporate',
            'Individual',
            'Customer type classification',
            'Active'
          ],
          [
            'priority_enum',
            'Low, Medium, High',
            'Medium',
            'Priority levels',
            'Active'
          ],
        ];
      case 'System Permissions':
        return [
          ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
          ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
          [
            'delete_customer',
            'SuperAdmin',
            'Delete',
            'Customer Data',
            'Restricted'
          ],
        ];
      case 'Security Classification':
        return [
          [
            'PII_Data',
            'High',
            'email, phone, address',
            'Encryption Required',
            'Active'
          ],
          ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
          [
            'Financial_Data',
            'Critical',
            'payment_info',
            'Strict Access Control',
            'Active'
          ],
        ];
      default:
        return [
          ['sample_name', 'sample_value', 'string', 'Active'],
          ['example_item', 'example_data', 'number', 'Pending'],
        ];
    }
  }

  String _getAddButtonText(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Property';
      case 'Entity Relationships':
        return 'Add Relationship';
      case 'Attribute Business Rules':
        return 'Add Rule';
      case 'Enumerated Values':
        return 'Add Enum';
      case 'System Permissions':
        return 'Add Permission';
      case 'Security Classification':
        return 'Add Classification';
      default:
        return 'Add Item';
    }
  }

  void _showAddModal(BuildContext context, String title) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getModalTitle(title),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields based on section type
                ..._buildModalFormFields(context, title),

                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Apply This',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getModalTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Object Property';
      case 'Entity Relationships':
        return 'Add Entity Relationship';
      case 'Attribute Business Rules':
        return 'Add Business Rule';
      case 'Enumerated Values':
        return 'Add Enumerated Value';
      case 'System Permissions':
        return 'Add System Permission';
      case 'Security Classification':
        return 'Add Security Classification';
      default:
        return 'Add Configuration';
    }
  }

  List<Widget> _buildModalFormFields(BuildContext context, String title) {
    switch (title) {
      case 'Object Details':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Property Name', 'property_name')),
              const SizedBox(width: 24),
              Expanded(
                  child:
                      _buildModalFormField(context, 'Value', 'Property Value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['string', 'number', 'boolean', 'date'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Required', ['No', 'Yes'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormField(context, 'Description', 'Property description'),
        ];
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Relationship Name', 'relationship_name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(
                      context, 'Target Entity', 'Target Entity')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Cardinality', ['1:1', '1:N', 'N:M'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownField(
              context, 'Status', ['Active', 'Pending', 'Inactive']),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Name', 'Enter name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(context, 'Value', 'Enter value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['string', 'number', 'boolean'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Status', ['Active', 'Inactive'])),
            ],
          ),
        ];
    }
  }

  String _getPlaceholderTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Object Details Configuration';
      case 'Entity Relationships':
        return 'Entity Relationships Configuration';
      case 'Attribute Business Rules':
        return 'Business Rules Configuration';
      case 'Enumerated Values':
        return 'Enumerated Values Configuration';
      case 'System Permissions':
        return 'System Permissions Configuration';
      case 'Security Classification':
        return 'Security Classification Configuration';
      default:
        return '$title Configuration';
    }
  }

  String _getPlaceholderDescription(String title) {
    switch (title) {
      case 'Object Details':
        return 'Configure detailed information about the object including its properties, metadata, and general settings.';
      case 'Entity Relationships':
        return 'Define relationships between this entity and other entities in your system.';
      case 'Attribute Business Rules':
        return 'Set up business rules and validation logic for the attributes of this entity.';
      case 'Enumerated Values':
        return 'Configure predefined values and options for enumerated fields.';
      case 'System Permissions':
        return 'Define access control and permission settings for this entity.';
      case 'Security Classification':
        return 'Set security levels and classification rules for data protection.';
      default:
        return 'Configure settings and options for this section.';
    }
  }

  // Helper method to get status information based on item title
  Map<String, dynamic> _getStatusInfo(String title) {
    switch (title) {
      case 'Object Details':
        return {
          'status': 'Partial Completion',
          'count': '1 Entity Detected',
          'backgroundColor': Color(0xFFFEF3C7),
          'textColor': Color(0xFF92400E),
        };
      case 'Properties & Methods':
        return {
          'status': 'Completed',
          'count': '25 Attributes',
          'backgroundColor': Color(0xFFD1FAE5),
          'textColor': Color(0xFF065F46),
        };
      default:
        return {
          'status': 'Missing',
          'count': '0 Configure',
          'backgroundColor': Color(0xFFFEE2E2),
          'textColor': Color(0xFF991B1B),
        };
    }
  }

  double _getFormTableMinWidth(String title) {
    switch (title) {
      case 'Object Details':
        return 800; // 5 columns + actions
      case 'Entity Relationships':
        return 880; // 5 columns + actions - increased to prevent overlap
      case 'Attribute Business Rules':
        return 750; // 5 columns + actions
      case 'Enumerated Values':
        return 850; // 5 columns + actions
      case 'System Permissions':
        return 750; // 5 columns + actions
      case 'Security Classification':
        return 800; // 5 columns + actions
      default:
        return 700;
    }
  }

  Widget _buildFormTableHeader(BuildContext context, String title) {
    List<String> headers = _getFormTableHeaders(title);
    List<double> widths = _getFormColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: headers.asMap().entries.map((entry) {
          int index = entry.key;
          String header = entry.value;
          double width = widths[index];

          return Container(
            width: width,
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.grey[700],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<Widget> _buildFormTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);
    
    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;
      return _buildGenericFormRow(context, index, title, rowData);
    }).toList();
  }

  Widget _buildGenericFormRow(BuildContext context, int index, String title, List<String> rowData) {
    List<double> widths = _getFormColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Build form fields based on column type
          ...rowData.asMap().entries.map((entry) {
            int colIndex = entry.key;
            String data = entry.value;
            double width = widths[colIndex];

            return Container(
              width: width,
              alignment: Alignment.centerLeft,
              child: _buildFormFieldByType(context, title, colIndex, data),
            );
          }),

          // Delete Action
          Container(
            width: 80,
            alignment: Alignment.center,
            child: IconButton(
              onPressed: () {
                // Handle delete
              },
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFieldByType(BuildContext context, String title, int columnIndex, String data) {
    List<String> dropdownColumns = _getDropdownColumns(title);
    List<String> headers = _getFormTableHeaders(title);

    if (columnIndex < headers.length && dropdownColumns.contains(headers[columnIndex])) {
      // Build dropdown with fixed height and 4px margin
      List<String> options = _getDropdownOptions(title, headers[columnIndex]);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: SizedBox(
          height: 33, // Fixed height to match TextFormField
          child: DropdownButtonFormField<String>(
            value: options.contains(data) ? data : options.first,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 20,
              ),
            ),
            iconSize: 20,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              // Handle change
            },
          ),
        ),
      );
    } else {
      // Build text field with 4px margin
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: TextFormField(
          initialValue: data,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            isDense: true,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
        ),
      );
    }
  }

  List<String> _getFormTableHeaders(String title) {
    List<String> baseHeaders = _getTableHeaders(title);
    return [...baseHeaders, 'ACTIONS'];
  }

  List<double> _getFormColumnWidths(String title) {
    List<double> baseWidths = _getColumnWidths(title);
    return [...baseWidths, 80]; // Add 80px for actions column
  }

  List<String> _getDropdownColumns(String title) {
    switch (title) {
      case 'Object Details':
        return ['TYPE', 'REQUIRED'];
      case 'Entity Relationships':
        return ['TYPE', 'CARDINALITY', 'STATUS'];
      case 'Attribute Business Rules':
        return ['ACTION', 'PRIORITY'];
      case 'Enumerated Values':
        return ['STATUS'];
      case 'System Permissions':
        return ['ROLE', 'ACCESS LEVEL', 'STATUS'];
      case 'Security Classification':
        return ['LEVEL', 'STATUS'];
      default:
        return ['TYPE', 'STATUS'];
    }
  }

  List<String> _getDropdownOptions(String title, String column) {
    switch (column) {
      case 'TYPE':
        if (title == 'Object Details') {
          return ['string', 'number', 'boolean', 'date'];
        } else if (title == 'Entity Relationships') {
          return ['One-to-One', 'One-to-Many', 'Many-to-Many'];
        }
        return ['string', 'number', 'boolean'];
      case 'REQUIRED':
        return ['YES', 'NO'];
      case 'CARDINALITY':
        return ['1:1', '1:N', 'N:M'];
      case 'STATUS':
        return ['Active', 'Pending', 'Inactive'];
      case 'ACTION':
        return ['reject', 'warn', 'format', 'accept'];
      case 'PRIORITY':
        return ['Low', 'Medium', 'High'];
      case 'ROLE':
        return ['User', 'Admin', 'SuperAdmin'];
      case 'ACCESS LEVEL':
        return ['Read', 'Write', 'Delete'];
      case 'LEVEL':
        return ['Low', 'Medium', 'High', 'Critical'];
      default:
        return ['Active', 'Inactive'];
    }
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}
