import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/nsl_hierarchy_provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/services/dummy_consolidated_service.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/speech_bubble_container.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/go_list_drawer.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/lo_list_drawer.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class NSLMobileNodeDetailScreen extends StatefulWidget {
  final NSLHierarchyData1 nodeData;
  final VoidCallback onBack;

  const NSLMobileNodeDetailScreen({
    super.key,
    required this.nodeData,
    required this.onBack,
  });

  @override
  State<NSLMobileNodeDetailScreen> createState() => _NSLMobileNodeDetailScreenState();
}

class _NSLMobileNodeDetailScreenState extends State<NSLMobileNodeDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isTransactionsExpanded = false;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;
  
  // Loading states
  bool _isLoadingNodeDetails = false;
  bool _isLoadingTransactions = false;
  
  // Data
  NslTreeSidePanel? _nodeDetails;
  MetricsInfo? _nodeTransactions;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedFromDate = DateTime.now().subtract(const Duration(days: 30));
    _selectedToDate = DateTime.now();
    _loadNodeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNodeData() async {
    final provider = Provider.of<NslHierarchyProvider>(context, listen: false);
    
    try {
      // First load node details (GO/LO counts and lists)
      await provider.onNodeTitleTap(NSLNode(
        id: widget.nodeData.id,
        title: widget.nodeData.title,
        type: widget.nodeData.type,
        totalBets: widget.nodeData.totalBets,
        betBreakdown: widget.nodeData.betBreakdown,
        financialSummary: widget.nodeData.financialSummary,
        levelName: widget.nodeData.levelName,
        level: widget.nodeData.level,
        originalData: widget.nodeData,
        children: [],
        isExpanded: false,
      ));
      
      // Then load transactions data with date range
      await provider.onDateRangeChanged(_selectedFromDate!, _selectedToDate!);
      
    } catch (e) {
      print('Error loading node data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          drawer: const CustomDrawer(),
          endDrawer: null, // Will be set dynamically when needed
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.transparent,
            foregroundColor: Colors.black,
            elevation: 6,
            automaticallyImplyLeading: false,
            titleSpacing: 0,
            title: Column(
              children: [
                Row(
                  children: [
                    // Hamburger menu icon
                    Builder(
                      builder: (context) => IconButton(
                        icon: const Icon(Icons.menu, color: Colors.black, size: 24),
                        onPressed: () => Scaffold.of(context).openDrawer(),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                      ),
                    ),
                  ],
                ),
                Divider(height: 2,)
              ],
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                _buildHeader(),
                _buildDateSelection(),
                _buildLatestTransactionsContainer(),
                _buildTabNavigation(),
                _buildScrollableTabContent(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal:16, vertical:8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Node circle and speech bubble
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Node circle
                Container(
                  width: 34,
                  height: 34,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: widget.nodeData.level == 'M4' 
                          ? const Color(0xFF0058FF) 
                          : NSLNode.getLevelColorMobile(widget.nodeData.level),
                      width: 3,
                    ),
                  ),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: widget.nodeData.level == 'M4' 
                          ? const Color(0xFF0058FF) 
                          : NSLNode.getLevelColorMobile(widget.nodeData.level),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        widget.nodeData.level,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Speech bubble with node info
                IntrinsicWidth(
                  child: SpeechBubbleContainer(
                    tailOffset: -20.0,
                    backgroundColor:const Color(0xFF0058FF),
                    borderColor: const Color(0xFF0058FF),
                    borderWidth: 2.0,
                    showTail: true,
                    tailDirection: TailDirection.left,
                    tailSize: 12.0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title section
                        GestureDetector(
                           onTap: widget.onBack,
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                            child: Text(
                              widget.nodeData.title.length > 20 
                                  ? '${widget.nodeData.title.substring(0, 15)}...' 
                                  : widget.nodeData.title,
                              textAlign: TextAlign.center,
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.titleLarge(context),
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: AppColors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        
                        // ID and NP row
                        Container(
                          margin: const EdgeInsets.only(left: 1, right: 1, bottom: 1),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                          decoration: BoxDecoration(
                            color: Color(0xFFCBDDFF),
                            //NSLNode.getLevelColor(widget.nodeData.level),
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'ID: ${widget.nodeData.employeeId ?? widget.nodeData.id}',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                'NP: ${widget.nodeData.totalBets}',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Back button
          GestureDetector(
            onTap: widget.onBack,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(0x26000000),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back,
                size: 20,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

// Replace the _buildDateSelection() method with this updated version:

Widget _buildDateSelection() {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    child: Column(
      children: [
        Divider(height: 2, color: Color(0xFFE3E3E3)),
        SizedBox(height: 5),
        
        // Single container for the date range
        GestureDetector(
          onTap: () => _selectDateRange(context),
          child: Container(
             padding: const EdgeInsets.symmetric(horizontal: 12,),
            // decoration: BoxDecoration(
            //   color: Colors.white,
            //   border: Border.all(color: const Color(0xFFB4B4B4)),
            //   borderRadius: BorderRadius.circular(4),
            // ),
            child: Row(
              children: [
                Container(
                  height: 24,
                  width:24,
                   decoration: BoxDecoration(
              color: Color(0x0D000000),
              shape: BoxShape.circle,
              
                ),
                  child: const Icon(Icons.calendar_today_outlined, size: 14, color: Colors.black)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${_formatDateWithDay(_selectedFromDate)} ',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                
                   Text(
                    '-   ',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                
                 Container(
                  height: 24,
                  width:24,
                   decoration: BoxDecoration(
              color: Color(0x0D000000),
              shape: BoxShape.circle,
              
                ),
                  child: const Icon(Icons.calendar_today_outlined, size: 14, color: Colors.black)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    ' ${_formatDateWithDay(_selectedToDate)}',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        SizedBox(height: 5),
        Divider(height: 2, color: Color(0xFFE3E3E3)),
      ],
    ),
  );
}

// Add these new helper methods to your class:

String _formatDateWithDay(DateTime? date) {
  if (date == null) return 'DD/MM/YY';
  
  final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  final monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  return '${dayNames[date.weekday - 1]}, ${date.day} ${monthNames[date.month - 1]} ${date.year}';
}

Future<void> _selectDateRange(BuildContext context) async {
  final DateTimeRange? picked = await showDateRangePicker(
    context: context,
    firstDate: DateTime(2020),
    lastDate: DateTime(2030),
    initialDateRange: DateTimeRange(
      start: _selectedFromDate ?? DateTime.now().subtract(const Duration(days: 30)),
      end: _selectedToDate ?? DateTime.now(),
    ),
  );
  
  if (picked != null) {
    setState(() {
      _selectedFromDate = picked.start;
      _selectedToDate = picked.end;
    });
    
    // Reload data with new date range - preserving your existing functionality
    await _loadNodeData();
  }
}


// - _selectFromDate()
// - _selectToDate()
  Widget _buildLatestTransactionsContainer() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(0xFFEBF5FF),
        // border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            padding: const EdgeInsets.only(left:12,right:12,top:12,bottom:8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Latest Transactions',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleLarge(context),
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isTransactionsExpanded = !_isTransactionsExpanded;
                    });
                  },
                  child: Text(
                    _isTransactionsExpanded ? 'View Back' : 'See all',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: const Color(0xFF0058FF),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Metrics cards
          if (!_isTransactionsExpanded) _buildCompactMetrics() else _buildExpandedMetrics(),
          
          // GO/LO row
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Consumer<NslHierarchyProvider>(
                    builder: (context, provider, child) {
                      return _buildGoLoCard(
                        'Total GO\'s',
                        provider.isLoadingNodeDetails 
                            ? null 
                            : provider.nodeDetails?.result?.goCount?.toString() ?? '0',
                        provider.isLoadingNodeDetails,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Consumer<NslHierarchyProvider>(
                    builder: (context, provider, child) {
                      return _buildGoLoCard(
                        'Total LO\'s',
                        provider.isLoadingNodeDetails 
                            ? null 
                            : provider.nodeDetails?.result?.loCount?.toString() ?? '0',
                        provider.isLoadingNodeDetails,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

   Widget _buildCompactMetrics() {
    final screenWidth = MediaQuery.of(context).size.width;
 
  final cardWidth = screenWidth *  0.36; 
  return Consumer<NslHierarchyProvider>(
    builder: (context, provider, child) {
      return Container(
        
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              SizedBox(
                width: cardWidth,
                child: _buildMetricCard(
                  'Total Transactions',
                  provider.isLoadingTransactions 
                      ? null 
                      : _formatTransactionCount(provider.nodeTransactions?.result?.totalTransactions ?? 2345500),
                  provider.isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                  width: cardWidth,
                child: _buildMetricCard(
                  'M3 Nodes',
                  '12',
                  false,
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                  width: cardWidth,
                child: _buildMetricCard(
                  'Revenue',
                  provider.isLoadingTransactions 
                      ? null 
                      : _formatNumber(provider.nodeTransactions?.result?.revenue?.toDouble() ?? 7000000),
                  provider.isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                  width: cardWidth,
                child: _buildMetricCard(
                  'Cost',
                  provider.isLoadingTransactions 
                      ? null 
                      : _formatNumber(provider.nodeTransactions?.result?.cost ?? 4320000),
                  provider.isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                  width: cardWidth,
                child: _buildMetricCard(
                  'Margin',
                  provider.isLoadingTransactions 
                      ? null 
                      : '${(provider.nodeTransactions?.result?.margin ?? 39.2).toStringAsFixed(1)}%',
                  provider.isLoadingTransactions,
                ),
              ),
             
              // Add more metric cards as needed
            ],
          ),
        ),
      );
    },
  );
}
 
 
 
  Widget _buildExpandedMetrics() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // First row - no animation (these are already visible in compact mode)
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Total Transactions',
                  _isLoadingTransactions 
                      ? null 
                      : _formatTransactionCount(_nodeTransactions?.result?.totalTransactions ?? 2345500),
                  _isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  'M3 Nodes',
                  '12',
                  false,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Second row - animate from right (these are new cards)
          _buildAnimatedMetricRow([
            _buildMetricCard(
              'Revenue',
              _isLoadingTransactions 
                  ? null 
                  : '\$${_formatNumber(_nodeTransactions?.result?.revenue?.toDouble() ?? 7000000)}',
              _isLoadingTransactions,
            ),
            _buildMetricCard(
              'Cost',
              _isLoadingTransactions 
                  ? null 
                  : '\$${_formatNumber(_nodeTransactions?.result?.cost ?? 4320000)}',
              _isLoadingTransactions,
            ),
          ], 0),
          const SizedBox(height: 8),
          // Third row - animate from right (this is a new card)
          _buildAnimatedMetricRow([
            _buildMetricCard(
              'Margin',
              _isLoadingTransactions 
                  ? null 
                  : '${(_nodeTransactions?.result?.margin ?? 39.2).toStringAsFixed(1)}%',
              _isLoadingTransactions,
            ),
            const SizedBox(), // Empty space for the second card
          ], 1),
        ],
      ),
    );
  }

  Widget _buildAnimatedMetricRow(List<Widget> cards, int rowIndex) {
    return TweenAnimationBuilder<double>(
      key: ValueKey('animated_row_$rowIndex'),
      duration: Duration(milliseconds: 400 + (rowIndex * 150)),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset((1 - value) * 120, (1 - value) * -30),
          child: Opacity(
            opacity: value,
            child: Row(
              children: [
                if (cards.isNotEmpty)
                  Expanded(child: cards[0]),
                const SizedBox(width: 8),
                if (cards.length > 1)
                  Expanded(child: cards[1])
                else
                  const Expanded(child: SizedBox()),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(String title, String? value, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Color(0xFFE5EAF3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          
          if (isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            Text(
              value ?? 'N/A',
              style: FontManager.getCustomStyle(
                fontSize: 20,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
             const SizedBox(height: 4),
            Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
         
        ],
      ),
    );
  }

  Widget _buildGoLoCard(String title, String? value, bool isLoading) {
    return GestureDetector(
      onTap: () => _openRightDrawer(title),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.white,width:0.5),
          borderRadius: BorderRadius.circular(6),
            boxShadow: [
                        BoxShadow(
                          color: Color(0x1F000000),
                          blurRadius: 1,
                          offset: const Offset(0, 1),
                        ),
                      ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                // const SizedBox(height: 4),
                if (isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  Text(
                    value ?? 'N/A',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
            const Icon(
              Icons.arrow_forward,
              color: Color(0xFF0058FF),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _openRightDrawer(String title) {
    final provider = Provider.of<NslHierarchyProvider>(context, listen: false);
    
    // First trigger the arrow tap to populate GO/LO data 
    provider.onArrowTap(title);
    
    // Prepare GO/LO data from API 
    List<Map<String, dynamic>>? goList;
    List<Map<String, dynamic>>? loList;
    
    if (provider.nodeDetails?.result?.gos != null && provider.nodeDetails!.result!.gos!.isNotEmpty) {
      goList = provider.nodeDetails!.result!.gos!.map((go) {
        return {
          'id': go.id,
          'name': go.name,
          'los': go.los?.map((lo) {
            return {
              'id': lo.id,
              'name': lo.name,
            };
          }).toList() ?? [],
        };
      }).toList();
      
      // Extract all LOs for the LO drawer
      loList = [];
      for (var go in provider.nodeDetails!.result!.gos!) {
        if (go.los != null) {
          for (var lo in go.los!) {
            loList.add({
              'id': lo.id,
              'name': lo.name,
              'parent_go': go.name,
              'parent_go_id': go.id,
            });
          }
        }
      }
    }

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Align(
          alignment: Alignment.centerRight,
          child: Material(
            color: Colors.transparent,
            child: title.contains('GO') 
                ? GoListDrawer(
                    onClose: () {
                      Navigator.of(context).pop();
                      // Clear the GO/LO panel state when closing (same as modern side panel)
                      provider.clearArrowSelection();
                    },
                    goList: goList,
                  )
                : LoListDrawer(
                    onClose: () {
                      Navigator.of(context).pop();
                      // Clear the GO/LO panel state when closing (same as modern side panel)
                      provider.clearArrowSelection();
                    },
                    loList: loList,
                  ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
      },
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFEBF5FF),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        indicatorSize: TabBarIndicatorSize.tab, // Ensures indicator matches tab width
      labelPadding: EdgeInsets.zero, // Remove label padding
        controller: _tabController,
        dividerColor: Colors.transparent,
        indicator: BoxDecoration(
          color: const Color(0xFF0058FF),
          borderRadius: BorderRadius.circular(12),
        ),
        labelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelLarge(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelLarge(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Color(0xFF373737),
          fontWeight: FontWeight.w400,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Color(0xFF373737),
        tabs: const [
          Tab(text: 'Standalone'),
          Tab(text: 'Consolidated'),
          Tab(text: 'BET Breakdown'),
        ],
      ),
    );
  }

  Widget _buildScrollableTabContent() {
    return AnimatedBuilder(
      animation: _tabController,
      builder: (context, child) {
        // Get the current tab index
        int currentIndex = _tabController.index;
        
        // Return the content for the current tab
        switch (currentIndex) {
          case 0:
            return _buildStandaloneTabContent();
          case 1:
            return _buildConsolidatedTabContent();
          case 2:
            return SizedBox();
            //  _buildBetBreakdownTabContent();
          default:
            return _buildStandaloneTabContent();
        }
      },
    );
  }

  Widget _buildStandaloneTabContent() {
    return Padding(
      padding: const EdgeInsets.only(left:16,top:12,bottom:12,right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCards(),
          const SizedBox(height: 12),
          _buildIncomeStatement(),
          // const SizedBox(height: 12),
          // _buildBalanceSheet(),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTabContent() {
    return FutureBuilder<List<dynamic>>(
      future: DummyConsolidatedService.loadDummyData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Padding(
            padding: EdgeInsets.all(20),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: Text(
                'Error loading consolidated data',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          );
        }

        final dataList = snapshot.data ?? [];
        Map<String, dynamic>? nodeData;
        
        for (var item in dataList) {
          if (item is Map<String, dynamic> && item['id'] == widget.nodeData.id) {
            nodeData = item;
            break;
          }
        }

        String totalGo = nodeData?['consolidated']?['total_gos'] ?? '45';
        String internalElimination = nodeData?['consolidated']?['internal_elimination'] ?? '134';
        String efficiency = nodeData?['consolidated']?['effeciency'] ?? '88.5%';
        String teamCoordination = nodeData?['consolidated']?['team_coordination'] ?? '39.2%';

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Total Go', totalGo, Colors.green),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Internal Elimination', internalElimination, Colors.blue),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Efficiency', efficiency, Colors.orange),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Team Coordination', teamCoordination,Colors.purple),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBetBreakdownTabContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BET Breakdown',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBetBreakdownCard('GO\'s', widget.nodeData.betBreakdown.gos.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('LO\'s', widget.nodeData.betBreakdown.los.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('NP Functions', widget.nodeData.betBreakdown.npFunctions.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Input/Output Stacks', widget.nodeData.betBreakdown.inputOutputStacks.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Subordinate NSL', widget.nodeData.betBreakdown.subordinateNsl.toString()),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildStandaloneTab(),
        _buildConsolidatedTab(),
        _buildBetBreakdownTab(),
      ],
    );
  }

  Widget _buildStandaloneTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCards(),
          const SizedBox(height: 16),
          _buildIncomeStatement(),
          const SizedBox(height: 16),
          // _buildBalanceSheet(),
        ],
      ),
    );
  }

  Widget _buildFinancialCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildFinancialCard(
                'Total Revenue',
                _isLoadingTransactions 
                    ? null 
                    : '\$${_formatNumber(_nodeTransactions?.result?.revenue?.toDouble() ?? 7000000)}',
                '+12.5% vs last month',
                Colors.green,
                _isLoadingTransactions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildFinancialCard(
                'Net Margin',
                _isLoadingTransactions 
                    ? null 
                    : '${(_nodeTransactions?.result?.margin ?? 39.2).toStringAsFixed(1)}%',
                '2.1% vs last month',
                Colors.blue,
                _isLoadingTransactions,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildFinancialCard(
                'Total Transactions',
                _isLoadingTransactions 
                    ? null 
                    : _formatTransactionCount(_nodeTransactions?.result?.totalTransactions ?? 15234),
                '+8.7% vs last month',
                Colors.orange,
                _isLoadingTransactions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildFinancialCard(
                'Utilization',
                '88.5%',
                '+3.2% vs last month',
                Colors.purple,
                false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialCard(String title, String? value, String trend, Color color, bool isLoading) {
    return Container(
      padding: const EdgeInsets.only(left:12,right:12,bottom:8,top:8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE8E8E8),width: 0.5),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
                      BoxShadow(
                        color: Color(0x14000000),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          if (isLoading)
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            )
          else
            Text(
              value ?? 'N/A',
              style: FontManager.getCustomStyle(
                fontSize: 20,
                fontFamily: FontManager.fontFamilyTiemposText,
               color:  color,
                fontWeight: FontWeight.w600,
              ),
            ),
          const SizedBox(height: AppSpacing.md),
          Text(
            trend,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatement() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0x0A000000),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Statement - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'For the Month Ended December 30, 2024',
            style: FontManager.getCustomStyle(
              fontSize: 10,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
            const SizedBox(height: 4),
          const Divider(  color: Color(0xFFB4B4B4),height:0.5),
          // const SizedBox(height: 8),
          _buildIncomeStatementContent(),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementContent() {
    if (_isLoadingTransactions) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      children: [
        _buildIncomeStatementRow('Service Revenue - Application Development', '\$3,150,000', '45.0% of Revenue'),
        _buildIncomeStatementRow('Service Revenue - QA & Testing', '\$3,150,000', '45.0% of Revenue'),
        _buildIncomeStatementRow('Service Revenue - Infrastructure Services', '\$3,150,000', '45.0% of Revenue'),
      ],
    );
  }

  Widget _buildIncomeStatementRow(String item, String amount, String percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  amount,
                  textAlign: TextAlign.right,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
             
             const SizedBox(height: 2), 
             Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyInter,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
          ),
           ],
            ),
        
        ),  ],
      ),
    );
  }

  Widget _buildBalanceSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Balance Sheet - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Divider(color: Color(0xFFB4B4B4)),
          const SizedBox(height: 8),
          if (_isLoadingTransactions)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: CircularProgressIndicator(),
              ),
            )
          else
            const Text('Balance sheet data will be displayed here'),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTab() {
    return FutureBuilder<List<dynamic>>(
      future: DummyConsolidatedService.loadDummyData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading consolidated data',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.red,
                fontWeight: FontWeight.w400,
              ),
            ),
          );
        }

        final dataList = snapshot.data ?? [];
        Map<String, dynamic>? nodeData;
        
        for (var item in dataList) {
          if (item is Map<String, dynamic> && item['id'] == widget.nodeData.id) {
            nodeData = item;
            break;
          }
        }

        String totalGo = nodeData?['consolidated']?['total_gos'] ?? '45';
        String internalElimination = nodeData?['consolidated']?['internal_elimination'] ?? '134';
        String efficiency = nodeData?['consolidated']?['effeciency'] ?? '88.5%';
        String teamCoordination = nodeData?['consolidated']?['team_coordination'] ?? '39.2%';

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Total Go', totalGo,Colors.green),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Internal Elimination', internalElimination,Colors.blue,),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Efficiency', efficiency,Colors.orange),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Team Coordination', teamCoordination,Colors.purple),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConsolidatedMetricCard(String title, String value, Color color) {
    return 
    
 
    Container(
        padding: const EdgeInsets.only(left:12,right:12,bottom:8,top:8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE8E8E8),width: 0.5),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
                      BoxShadow(
                        color: Color(0x14000000),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
       
            Text(
              value ,
              style: FontManager.getCustomStyle(
                fontSize: 20,
                fontFamily: FontManager.fontFamilyTiemposText,
               color:  color,
                fontWeight: FontWeight.w600,
              ),
            ),
          
          
        ],
      ),
    );
  }

  Widget _buildBetBreakdownTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BET Breakdown',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBetBreakdownCard('GO\'s', widget.nodeData.betBreakdown.gos.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('LO\'s', widget.nodeData.betBreakdown.los.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('NP Functions', widget.nodeData.betBreakdown.npFunctions.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Input/Output Stacks', widget.nodeData.betBreakdown.inputOutputStacks.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Subordinate NSL', widget.nodeData.betBreakdown.subordinateNsl.toString()),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime? date) {
    if (date == null) return 'DD/MM/YY';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
  }

  String _formatNumber(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  String _formatTransactionCount(int value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toString();
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedFromDate) {
      setState(() {
        _selectedFromDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedToDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedToDate) {
      setState(() {
        _selectedToDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }
}
